<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->decimal('credit_limit', 10, 2)->default(0.00)->after('balance');
            $table->boolean('credit_enabled')->default(false)->after('credit_limit');
            $table->integer('billing_cycle')->nullable()->after('credit_enabled');
            $table->date('billing_cycle_start')->nullable()->after('billing_cycle');


        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->dropColumn('credit_limit');
            $table->dropColumn('credit_enabled');
            $table->dropColumn('billing_cycle');
            $table->dropColumn('billing_cycle_start');
        });
    }
};
