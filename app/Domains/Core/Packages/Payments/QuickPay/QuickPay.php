<?php

declare(strict_types=1);

namespace Core\Packages\Payments\QuickPay;

use Exception;
use Illuminate\Support\Str;
use QuickPay\QuickPay as QPay;
use Illuminate\Support\Facades\Auth;
use Main\Modules\Shipment\Models\Shipment;
use Core\Packages\Payments\AbstractPayment;
use Core\Packages\Payments\Interfaces\PaymentInterface;
use App\Domains\app\Modules\Returns\Models\ReturnShipmentCardPayment;

class QuickPay extends AbstractPayment implements PaymentInterface
{
    public int $merchantID;
    public string $apiKey;
    public Object $client;
    public function __construct()
    {
    }

    /**
     * initializes QuickPay
     * @param int $merchantID
     * @param string $apiKey
     */
    public function init(int $merchantID, string $apiKey): void
    {
        $this->merchantID = $merchantID;
        $this->apiKey = $apiKey;

        $this->client = new QPay(':' . $this->apiKey);
    }

    /**
     * creates payment and a link for the same, throws an exception in case of error
     * @param float $amount
     * @param int $transactionID
     * @param string $currency
     * @return string $url
     */
    public function createPayment(float $amount, int $companyCode, string $currency = 'DKK'): array
    {
        $payment = $this->client->request->post('/payments', [
            'order_id'  =>  Str::random(5) . '_' . $companyCode,
            'currency'  =>  $currency
        ]);

        if ($payment->httpStatus() == 400) {
            $responseBody = $payment->asArray();
            $errorsData = [
                'company_code' => Auth::user()->company_code,
                'transaction_code' => null,
                'sales_invoice_code' => null,
                'shipment_code' => null,
                'platform' => "Quickpay",
                'exception' => $responseBody,
                'description' => $responseBody['message'],
                'payment_id' => null,
                'request_params' => null
            ];
            createErrorLog($errorsData);
            throw new \Exception($responseBody['message'], $payment->httpStatus());
        }

        $paymentObject = $payment->asObject();
        $endpoint = sprintf("/payments/%s/link", $paymentObject->id);

        //Issue a put request to create payment link
        $link = $this->client->request->put($endpoint, [
            'amount' => $amount * 100, //amount in cents,
            // 'continue_url' => url('/payment/continue'),
            'callback_url' => url('api' . config('quickpay.addFundcallbackUrl')),
            'cancel_url' => url('api' . config('quickpay.cancelUrl')),
            'auto_capture'  => true,
            'framed'  => true,
            'language' => 'DA'
        ]);

        if ($link->httpStatus() !== 200) {
            $responseBody = $link->asArray();
            $errorsData = [
                'company_code' => Auth::user()->company_code,
                'transaction_code' => null,
                'sales_invoice_code' => null,
                'shipment_code' => null,
                'platform' => "Quickpay",
                'exception' => $responseBody,
                'description' => $responseBody['message'],
                'payment_id' => null,
                'request_params' => null
            ];
            createErrorLog($errorsData);
            throw new \Exception($responseBody['message'], $payment->httpStatus());
        }

        return [
            'link'   =>   $link->asObject()->url,
            'payment_id'    =>   $paymentObject->id,
        ];
    }

    /**
     * creates payment and a link for the same, throws an exception in case of error
     * @param float $amount
     * @param int $transactionID
     * @param string $token
     * @param string $currency
     * @return string $url
     */
    public function makePaymentFromCard(float $amount, int $companyCode, string $token, string $currency = 'DKK'): int
    {
        $payment = $this->client->request->post('/payments', [
            'order_id'  =>  $companyCode == 0 ? Str::random(5) . '_' . rand(11111, 99999) : Str::random(5) . '_' . $companyCode,
            'currency'  =>  $currency
        ]);

        if ($payment->httpStatus() == 400) {
            $responseBody = $payment->asArray();
            $errorsData = [
                'company_code' => Auth::user()->company_code,
                'transaction_code' => null,
                'sales_invoice_code' => null,
                'shipment_code' => null,
                'platform' => "Quickpay",
                'exception' => $responseBody,
                'description' => $responseBody['message'],
                'payment_id' => null,
                'request_params' => null
            ];
            createErrorLog($errorsData);
            throw new \Exception($responseBody['message'], $payment->httpStatus());
        }

        $paymentObject = $payment->asObject();
        $endpoint = sprintf("/payments/%s/authorize", $paymentObject->id);

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.quickpay.net' . $endpoint,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
                "card":{
                    "token":"' . $token . '"
                },
                "amount":' . ($amount * 100) . ',
                "auto_capture": true
            }',
            CURLOPT_HTTPHEADER => array(
                'content-type: application/json',
                'Accept-Version: v10',
                'QuickPay-Callback-Url: ' . url('api' . config('quickpay.addFundcallbackUrl')),
                'Authorization: Basic OmQzM2RlMjRkYTk5NjE5OWY4ZGYxMzAwNzZjODQ5ODlhMzg2ZmMxNjI1N2M1MDhkY2M3MGViM2NlMTNkNjNiYWM='
            ),
        ));

        $response = curl_exec($curl);
        $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        $response_data = json_decode($response, true);
        $error_code = array_key_exists('error_code', $response_data) ? $response_data['error_code'] : [];
        curl_close($curl);

        if ($error_code == 'cannot_accept_test_payments') {
            $errorsData = [
                'company_code' => Auth::user()->company_code,
                'transaction_code' => null,
                'sales_invoice_code' => null,
                'shipment_code' => null,
                'platform' => "Quickpay",
                'exception' => $response_data,
                'description' => "Can not accept test payments",
                'payment_id' => $paymentObject->id,
                'request_params' => null
            ];
            createErrorLog($errorsData);
            throw new Exception("Can not accept test payments", 402);
        }

        if ($httpcode !== 202) {
            $errorsData = [
                'company_code' => Auth::user()->company_code,
                'transaction_code' => null,
                'sales_invoice_code' => null,
                'shipment_code' => null,
                'platform' => "Quickpay",
                'exception' => $response_data,
                'description' => "Error occur while making payment from card",
                'payment_id' => $paymentObject->id,
                'request_params' => null
            ];
            createErrorLog($errorsData);
            throw new \Exception("Something went wrong", $httpcode);
        }

        return $paymentObject->id;
    }

    /**
     * creates payment and a link for the same, throws an exception in case of error
     * @param float $amount
     * @param int $transactionID
     * @param string $token
     * @param string $currency
     * @return string $url
     */
    public function makePaymentFromCardAndGetPaymentId(float $amount, int $transactionID, string $token, string $currency = 'DKK'): int
    {
        info('makePaymentFromCardAndGetPaymentId start');
        info('makePaymentFromCardAndGetPaymentId : transactionID');
        info($transactionID);
        $payment = $this->client->request->post('/payments', [
            'order_id'  =>  $transactionID == 0 ? 'SHI' . Str::random(3) . '_' . rand(11111, 99999) : 'SHI' . Str::random(3) .  '_' . $transactionID,
            'currency'  =>  $currency
        ]);

        info('makePaymentFromCardAndGetPaymentId : 1. response');
        info([$payment]);

        if ($payment->httpStatus() == 400) {
            $responseBody = $payment->asArray();
            throw new \Exception($responseBody['message'], $payment->httpStatus());
        }

        $paymentObject = $payment->asObject();
        $endpoint = sprintf("/payments/%s/authorize", $paymentObject->id);
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.quickpay.net' . $endpoint,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
                "card":{
                    "token":"' . $token . '"
                },
                "amount":' . ($amount * 100) . ',
                "auto_capture": true
            }',
            CURLOPT_HTTPHEADER => array(
                'content-type: application/json',
                'Accept-Version: v10',
                'QuickPay-Callback-Url: ' . url('api' . config('quickpay.cardCallBackUrl') . '/' . Auth::user()->company_code),
                'Authorization: Basic OmQzM2RlMjRkYTk5NjE5OWY4ZGYxMzAwNzZjODQ5ODlhMzg2ZmMxNjI1N2M1MDhkY2M3MGViM2NlMTNkNjNiYWM='
            ),
        ));

        $response = curl_exec($curl);
        $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        $response_data = json_decode($response, true);
        info('makePaymentFromCardAndGetPaymentId : 2. quick pay response');
        info($response_data);
        $error_code = array_key_exists('error_code', $response_data) ? $response_data['error_code'] : [];
        curl_close($curl);

        if ($error_code == 'cannot_accept_test_payments') {
            throw new Exception("Can not accept test payments", 400);
        }

        if ($httpcode !== 202) {
            throw new \Exception("Something went wrong", $httpcode);
        }

        return $paymentObject->id;
    }

    /**
     * creates payment and a link for the same, throws an exception in case of error
     * @param float $amount
     * @param int $transactionID
     * @param string $token
     * @param string $currency
     * @return string $url
     */
    public function makeAndAuthorizePaymentFromCard(float $amount, int $transactionID, string $token, string $currency = 'DKK'): int
    {
        $payment = $this->client->request->post('/payments', [
            'order_id'  =>  $transactionID == 0 ? Str::random(5) . '_' . rand(11111, 99999) : Str::random(5) . '_' . $transactionID,
            'currency'  =>  $currency
        ]);

        info("makeAndAuthorizePaymentFromCard");
        info([$payment]);
        if ($payment->httpStatus() == 400) {
            $responseBody = $payment->asArray();
            throw new \Exception($responseBody['message'], $payment->httpStatus());
        }

        $paymentObject = $payment->asObject();
        $endpoint = sprintf("/payments/%s/authorize", $paymentObject->id);


        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.quickpay.net' . $endpoint,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
                "card":{
                    "token":"' . $token . '"
                },
                "amount":' . ($amount * 100) . '
            }',
            CURLOPT_HTTPHEADER => array(
                'content-type: application/json',
                'Accept-Version: v10',
                'QuickPay-Callback-Url: ' . url('api' . config('quickpay.cardCallBackUrl') . '/' . Auth::user()->company_code),
                'Authorization: Basic OmQzM2RlMjRkYTk5NjE5OWY4ZGYxMzAwNzZjODQ5ODlhMzg2ZmMxNjI1N2M1MDhkY2M3MGViM2NlMTNkNjNiYWM='
            ),
        ));

        $response = curl_exec($curl);
        $response_data = json_decode($response, true);

        info("authorization response");
        info($response_data);
        $error_code = array_key_exists('error_code', $response_data) ? $response_data['error_code'] : [];

        $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        //        echo $response;
        //        //Issue a put request to create payment link
        //        $link = $this->client->request->post($endpoint, [
        //            'amount' => $amount*100, //amount in cents,
        //            'card' => ['token' => $token],
        //            'callback_url' => url('api'.config('quickpay.cardCallBackUrl').'/'.Auth::user()->company_code),
        //            'callback-url' => url('api'.config('quickpay.cardCallBackUrl').'/'.Auth::user()->company_code),
        //            'callbackurl' => url('api'.config('quickpay.cardCallBackUrl').'/'.Auth::user()->company_code),
        //            'auto_capture'  => true
        //        ]);
        //

        if ($error_code == 'cannot_accept_test_payments') {
            throw new Exception("Can not accept test payments", 402);
        }
        if ($httpcode !== 202) {
            throw new \Exception("Something went wrong", $httpcode);
        }


        return $paymentObject->id;
    }

    /**
     * creates payment and a link for the same, throws an exception in case of error
     * @param float $amount
     * @param int $shipmentCode
     * @param string $currency
     * @return string $url
     */
    public function createReturnShippingPayment(float $amount, int $shipmentCode, string $currency = 'DKK'): array
    {
        $shipment = Shipment::where('shipment_code', $shipmentCode)->first();
        $payment = $this->client->request->post('/payments', [
            'order_id'  =>  $shipment->company_code . '_' . $shipmentCode,
            'currency'  =>  $currency
        ]);

        if ($payment->httpStatus() == 400) {
            $responseBody = $payment->asArray();
            throw new \Exception($responseBody['message'], $payment->httpStatus());
        }

        $paymentObject = $payment->asObject();
        $endpoint = sprintf("/payments/%s/link", $paymentObject->id);

        //Issue a put request to create payment link
        $link = $this->client->request->put($endpoint, [
            'amount' => $amount * 100, //amount in cents,
            'callback_url' => url('api' . config('quickpay.returnPortalcallbackUrl')),
            'cancel_url' => url('api' . config('quickpay.cancelUrl')),
            'auto_capture'  => true,
            'framed'  => true,
            'language' => 'DA'
        ]);

        if ($link->httpStatus() !== 200) {
            $responseBody = $link->asArray();
            throw new \Exception($responseBody['message'], $payment->httpStatus());
        }

        ReturnShipmentCardPayment::updateOrCreate([
            'qp_payment_id' => $paymentObject->id,
            'shipment_code' => $shipment->shipment_code
        ], [
            'rscp_code' => generateHash(),
            'amount' => $amount,
            'status' => 'AUTHORIZE'
        ]);

        return [
            'payment_url' => $link->asObject()->url,
            'payment_id' => $paymentObject->id,
        ];
    }

    public function savedCards(): array
    {
        $cards = $this->client->request->get('/cards');

        if ($cards->httpStatus() == 403) {
            $responseBody = $cards->asArray();
            throw new \Exception($responseBody['message'], $cards->httpStatus());
        }
        return $cards->asArray();
    }

    public function addCard(): int
    {
        $cards = $this->client->request->post('/cards');

        if ($cards->httpStatus() == 403) {
            $responseBody = $cards->asArray();
            throw new \Exception($responseBody['message'], $cards->httpStatus());
        }
        return $cards->asObject()->id ?? throw new \Exception($cards->asArray()['message'], $cards->httpStatus());
    }

    public function authorizeCardLink($cardId): string
    {
        $cards = $this->client->request->put('/cards/' . $cardId . '/link', [
            'callbackurl' => url('api' . config('quickpay.cardCallBackUrl') . '/' . Auth::user()->company_code),
            'framed'  => true,
            'language' => 'DA'
        ]);

        if ($cards->httpStatus() == 403) {
            $responseBody = $cards->asArray();
            throw new \Exception($responseBody['message'], $cards->httpStatus());
        }
        return $cards->asObject()->url ?? throw new \Exception($cards->asArray()['message'], $cards->httpStatus());
    }

    public function getCardToken($cardId): string
    {
        $cards = $this->client->request->post('/cards/' . $cardId . '/tokens');

        if ($cards->httpStatus() == 403) {
            $responseBody = $cards->asArray();
            throw new \Exception($responseBody['message'], $cards->httpStatus());
        }
        return $cards->asObject()->token ?? throw new \Exception($cards->asArray()['message'], $cards->httpStatus());
    }

    public function capturePayment($paymentId, $amount)
    {
        $payment = $this->client->request->post('/payments/' . $paymentId . '/capture', [
            'amount' => $amount * 100 //amount in cents,
        ]);

        if ($payment->httpStatus() != 202) {
            $responseBody = $payment->asArray();
            throw new \Exception($responseBody['message'], $payment->httpStatus());
        }
    }

    public function createRefundPayment($paymentId, $shipmentCode, $amount)
    {
        // $link = $this->client->request->post("payments/$paymentId/refund", [
        //     'amount' => $amount * 100, // amount in cents,
        // ]);

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://api.quickpay.net/payments/$paymentId/refund",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => array('amount' => ($amount * 100)),
            CURLOPT_HTTPHEADER => array(
                'Accept-Version: v10',
                'Accept: application/json',
                'Authorization: Basic OmQzM2RlMjRkYTk5NjE5OWY4ZGYxMzAwNzZjODQ5ODlhMzg2ZmMxNjI1N2M1MDhkY2M3MGViM2NlMTNkNjNiYWM=',
                'QuickPay-Callback-Url: ' . url('api' . config('quickpay.refundcallbackUrl')),
            ),
        ));

        $response = curl_exec($curl);
        $response_data = json_decode($response, true);
        $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);


        if ($httpcode !== 202) {
            $responseBody = $response_data;
            $errorsData = [
                'company_code' => Auth::user()->company_code,
                'transaction_code' => null,
                'sales_invoice_code' => null,
                'shipment_code' => $shipmentCode,
                'platform' => "Quickpay Return Portal",
                'exception' => $responseBody,
                'description' => "End user payment cannot be refunded.",
                'payment_id' => $paymentId,
                'request_params' => null
            ];
            createErrorLog($errorsData);
            throw new \Exception($responseBody['message'], $httpcode);
        }

        return $paymentId;
    }

    public function cancelPayment($paymentId)
    {
        $payment = $this->client->request->post('/payments/' . $paymentId . '/cancel');
        if ($payment->httpStatus() != 202) {
            info("error found in cancel payment");
            $responseBody = $payment->asArray();
            throw new \Exception($responseBody['message'], $payment->httpStatus());
        }
    }

    public function getPayment($paymentId)
    {
        $payment = $this->client->request->get('/payments/' . $paymentId);

        return $payment->asArray();
    }
}
