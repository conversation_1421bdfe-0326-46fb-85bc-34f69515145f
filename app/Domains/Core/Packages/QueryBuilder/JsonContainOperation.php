<?php

namespace Core\Packages\QueryBuilder;

/**
 * Class NotInOperation
 *
 * @package Core\Packages\QueryBuilder
 */
class JsonContainOperation extends QueryBuilder
{
    /**
     * method.
     *
     * @var string
     */
    protected $method = 'whereRaw';

    /**
     * name.
     *
     * @var string
     */
    protected $name = 'json_contain';

    /**
     * operator.
     *
     * @var string
     */
    protected $operator = 'whereRaw';


    /**
     * getParameters.
     * @param string $column
     * @param $value
     * @return array
     */
    public function getParameters(string $column, $value): array
    {
        return [$column,$this->operator.'('.("JSON_CONTAINS($column, '\"$value\"', '$.')").')'];
    }


}
