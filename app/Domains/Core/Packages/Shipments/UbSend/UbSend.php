<?php

namespace Core\Packages\Shipments\UbSend;

use stdClass;
use Carbon\Carbon;
use App\Models\Settings;
use Core\Response\AbstractResponse;
use Illuminate\Support\Facades\Log;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use App\Models\UBSend as ModelsUBSend;
use Core\Packages\Shipments\AbstractShipment;
use Main\Modules\Transaction\Models\Transaction;
use Main\Modules\Transaction\Repositories\TransactionRepository;

class UbSend extends AbstractShipment
{
    private string $accessToken;

    public function __construct()
    {
        $this->getToken();
    }

    public function getToken(): void
    {
        if (Cache::has('ubsend_access_token')) {
            $this->accessToken = Cache::get('ubsend_access_token');
            return;
        }
        $response = Http::withHeaders(['clientId' => config('ubsend.client_id')])
            ->post(config('ubsend.apiUrl') . '/auth/login', [
                'username' => config('ubsend.username'),
                'password' => config('ubsend.password')
            ]);

        if ($response->failed() || $response->clientError() || $response->serverError()) {
            $body = $response->json();
            throw new \Exception($body['error'] ?? "Cannot login to ubsend");
        }
        $body = $response->json();
        $this->accessToken = $body['accessToken'];
        Cache::put('ubsend_access_token', $body['accessToken'], 3600);
        Cache::put('ubsend_refresh_token', $body['refreshToken'], 3600);
    }

    public function refreshToken(): void
    {
        $response = Http::withHeaders(['clientId' => config('ubsend.client_id')])
            ->withToken($this->accessToken)
            ->post(config('ubsend.apiUrl') . '/auth/refresh-token');

        if ($response->failed() || $response->clientError() || $response->serverError()) {
            $body = $response->json();
            throw new \Exception($body['error']);
        }
        $body = $response->json();
        $this->accessToken = $body['accessToken'];
        Cache::put('ubsend_access_token', $body['accessToken'], 3600);
        Cache::put('ubsend_refresh_token', $body['refreshToken'], 3600);
    }

    public function createShipment(array $arData): array
    {
        $settings = Settings::where('module', 'ADDRESS')->get();
        $collectionInfo = $arData['collection_info'];

        /** dimensions added from admin side **/
        $dimensionKey = $arData['carrierProduct']['carrier'] . '-' . $arData['carrierProduct']['productId'];
        $dimension = Settings::where('module', 'DIMENSION')->where('key', $dimensionKey)->first();
        if (empty($dimension)) {
            $errorsData = [
                'company_code' => Auth::user()->company_code,
                'transaction_code' => $this->getTransaction($arData['shipment_code']),
                'sales_invoice_code' => null,
                'shipment_code' => $arData['shipment_code'],
                'platform' => "Shipvagoo",
                'exception' => null,
                'description' => "Dimensions not found for this carrier product.",
                'payment_id' => null,
                'request_params' => null
            ];
            createErrorLog($errorsData);
            throw new \Exception("Dimensions not found for this carrier product.");
        }
        $value = json_decode($dimension->value);
        $width = $value->width;
        $height = $value->height;
        $length = $value->length;
        /** end fetch dimensions */

        $internationalCarriers = $settings->where('module', 'ADDRESS')->pluck('key')->toArray();

        if (in_array($arData['carrierProduct']['carrier'], $internationalCarriers)) {
            $addressJsonString = $settings->where('module', 'ADDRESS')->where('key', $arData['carrierProduct']['carrier'])->pluck('value')[0];
            $address = json_decode($addressJsonString);
            $zip = $address->zip;
            $customerAddresss = $address->address;
            $customerCity = $address->city;
            $customerName = $address->customer_name;
            $country = $address->country;

            $arData['sender']['postCode'] = $zip;
            $arData['sender']['firstName'] = auth()->user()->company->company_name;
            $arData['sender']['lastName'] = $customerName;
            $arData['sender']['country'] = $arData['recipient']['country'];
            $arData['sender']['city'] = $customerCity;
            $arData['sender']['addressLine1'] = $customerAddresss;
            $arData['sender']['country'] = $country;
        }
        if (isset($arData['recipient']['contactInfo']['telephone'])) {
            $arData['recipient']['contactInfo']['mobile'] = $arData['recipient']['contactInfo']['telephone'];
        }

        ($arData['recipient']['lastName'] == "") ? $arData['recipient']['lastName'] = '-' : $arData['recipient']['lastName'];
        ($arData['recipient']['firstName'] == "") ? $arData['recipient']['firstName'] = '-' : $arData['recipient']['firstName'];
        $shipmentData = [];
        $parcels = array();
        $arData['parcels'] = $arData['parcels'] ?? null;
        if ($arData['parcels'] != null && count($arData['parcels']) > 0) {
            foreach ($arData['parcels'] as $parcel) {
                for ($i = 0; $i < (int)$parcel['count']; $i++) {
                    $parcels[] = array(
                        "count" => 1,
                        "width" => $width,
                        "height" => $height,
                        "length" => $length,
                        "weight" => $parcel['weight'] ?? null,
                        "description" => $parcel['description'] ?? null,
                        "orderNumber" => $parcel['orderNumber'] ?? null,
                        "carrierReference" => $parcel['carrierReference'] ?? null,
                        "cashOnDeliveryAmount" => $parcel['cashOnDeliveryAmount'] ?? null,
                        "carrierIntegrationReference" => $parcel['carrierIntegrationReference'] ?? null
                    );
                }
            }
        }
        $shipmentData['sender'] = $arData['sender'];
        $shipmentData['recipient'] = $arData['recipient'];
        $shipmentData['collectionInfo'] = $collectionInfo ?? null;
        $shipmentData['shipmentType'] = $arData['shipment_type'] ?? null;
        $shipmentData['orderNumber'] = $arData['shipment_code'] ?? null;
        $shipmentData['description'] = $arData['description'] ?? null;
        $shipmentData['deliveryInstructions'] = $arData['delivery_instructions'] ?? null;
        $shipmentData['recipientPays'] = $arData['recipient_pays'] ?? null;
        $shipmentData['parcels'] = $parcels ?? null;
        $shipmentData['carrierProduct'] = $arData['carrierProduct'] ?? null;
        $shipmentData['type'] = 'PDF';
        $shipmentData['layout'] = '4R';
        $shipmentData['shipmentType'] = $arData['shipment_type'] ?? null;
        $this->getToken();

        if (strlen($shipmentData['sender']['country']) != 2) {
            $shipmentData['sender']['country'] = countryCode($shipmentData['sender']['country']);
        }

        if (strlen($shipmentData['recipient']['country']) != 2) {
            $shipmentData['recipient']['country'] = countryCode($shipmentData['recipient']['country']);
        }

        // shipMer#03
        if (isset($arData['type']) && $arData['type'] == "RETURN") {
            if (isset($arData['carrier_product']) && isset($arData['carrier_product']['carrier'])) {
                if (isset($arData['recipient']['company'])) {
                    $shipmentData['recipient']['lastName'] = $arData['recipient']['firstName'] . ' ' . $arData['recipient']['lastName'];
                    $shipmentData['recipient']['firstName'] = $arData['recipient']['company'] . " /";
                }
            }
        }

        $response = Http::withHeaders(['clientId' => $arData['ubsend_client_id']])
            ->withToken($this->accessToken)
            ->post(config('ubsend.apiUrl') . '/shipments/label', $shipmentData);

        if ($response->failed() || $response->clientError() || $response->serverError()) {
            $body = $response->json();
            info("generate label failed");
            error_log(json_encode($body));
            $errorsData = [
                'company_code' => Auth::user()->company_code,
                'transaction_code' => $this->getTransaction($arData['shipment_code']),
                'sales_invoice_code' => null,
                'shipment_code' => $arData['shipment_code'],
                'platform' => "Ubsend",
                'exception' => $body,
                'description' => "Label generation failed.",
                'payment_id' => null,
                'request_params' => $shipmentData
            ];
            createErrorLog($errorsData);
            throw new \Exception(json_encode($body));
        }


        $body = $response->json();
        error_log($body['id']);

        /**
         * Essentially this means that the shipment was created
         */
        if (isset($body['label'])) {
            $body['label_zpl'] = $body['label'];
            $body['label_a6'] = $body['label'];
        } else {
            throw new \Exception("Failed to create shipping label.");
        }

        return $body;
    }

    public function getTransaction(int $shipmentCode)
    {
        try {
            $transactionRepository = app(TransactionRepository::class);
            $transaction = $transactionRepository->getTransactionByShipmentCode($shipmentCode, 'RETURN_ADD_FUNDS', 'COMPLETED');
            return $transaction->transaction_code;
        } catch (\Exception $e) {
            info($e->getMessage());
            return null;
        }
    }

    public function parcelShops(array $searchData)
    {
        $params = [];
        foreach ($searchData as $key => $value) {
            if ($key == 'city') {
                continue;
            }
            if ($key == 'address') {
                $params[] = $key . '=' . $value . ' ' . ($searchData['city'] ?? '');
            } else {
                $params[] = $key . '=' . $value;
            }
        }

        $response = Http::withHeaders(['clientId' => config('ubsend.client_id')])
            ->withToken($this->accessToken)
            ->get(config('ubsend.apiUrl') . '/parcel-shops/parcel-shops-geo?' . implode('&', $params));

        if ($response->failed() || $response->clientError() || $response->serverError()) {
            $body = $response->json();
            info("parcel shops exception from ubsend");
            info($response);
            throw new \Exception($body['error']);
        }
        $array = json_decode($response, true);
        $parcelShops = [];
        foreach ($array as $key => $value) {
            // if parcel shops exists in response
            if (isset($value['parcelShop'])) {

                // if the country in parcel shop is not empty or not equal to country in request then skip the iteration
                if ($value['parcelShop']['country'] != '' &&  $value['parcelShop']['country'] != $searchData['country']) {
                    continue;
                }

                // if the country in parcel shop is either empty or equal to the country coming in request
                $parcelShops[] = $value;
            }
        }

        return $parcelShops;
    }

    public function nearestParcelShop(array $address)
    {
        $parcelShops = $this->parcelShops($address);
        $minDistance = PHP_FLOAT_MAX;
        $nearestParcelShop = new stdClass();
        if (count($parcelShops) == 0) {
            return null;
        }
        foreach ($parcelShops as $shop) {
            if ($shop['distance'] < $minDistance) {
                $nearestParcelShop = $shop['parcelShop'];
                $minDistance = $shop['distance'];
            }
        }

        return $nearestParcelShop;
    }

    public function shipmentEvents(string $shipmentID)
    {
        $response = Http::withHeaders(['clientId' => config('ubsend.client_id')])
            ->withToken($this->accessToken)
            ->get(config('ubsend.apiUrl') . '/reporting/shipments/' . $shipmentID . '/events');

        if ($response->failed() || $response->clientError() || $response->serverError()) {
            $body = $response->json();
            throw new \Exception(json_encode($body));
        }
        return $response->json();
    }

    public function getUpdatedEventShipments($received_after = '2023-03-13T05:43:35', $received_before = '2023-03-30T05:43:35', $clientId = null)
    {
        $date = Carbon::now()->addDays(1)->format("Y-m-d H:m:s");
        $received_before = str_replace(' ', 'T', $date);
        $minusdate = Carbon::now()->subDays(1)->format("Y-m-d H:m:s");
        $received_after = str_replace(' ', 'T', $minusdate);

        $response = Http::withHeaders(['clientId' => config('ubsend.client_id')])
            ->withToken($this->accessToken)
            ->withHeaders(['ClientId' => $clientId->client_id])
            ->get(config('ubsend.apiUrl') . '/reporting/shipments/events?client_id=' . $clientId->client_id . '&received_after=' . $received_after . '&received_before=' . $received_before);

        if ($response->failed() || $response->clientError() || $response->serverError()) {
            $body = $response->json();
            throw new \Exception(json_encode($body));
        }
        return $response->json();
    }

    public function parcelShop(mixed $parcelShopID, string $carrierCode)
    {
        $response = Http::withHeaders(['clientId' => config('ubsend.client_id')])
            ->withToken($this->accessToken)
            ->get(config('ubsend.apiUrl') . '/parcel-shops/parcel-shop?carrier=' . $carrierCode . '&parcel_shop_id=' . $parcelShopID);

        if ($response->failed() || $response->clientError() || $response->serverError()) {
            $body = $response->json();
            throw new \Exception(json_encode($body));
        }
        return $response->json();

        //comment
    }
}
