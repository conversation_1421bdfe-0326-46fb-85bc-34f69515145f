<?php
declare(strict_types=1);

namespace Core\Authentication;

use Core\Contracts\Authentication\Authentication;
use Core\Exceptions\AuthenticationException;
use Main\Modules\Setting\Repositories\ModuleRepository;
use Main\Modules\User\Entities\User;
use Main\Modules\User\Repositories\UserRepository;

class AbstractAuthentication implements Authentication
{
    /**
     * @var UserRepository
     */
    protected UserRepository $userRepository;
    protected ModuleRepository $moduleRepository;

    protected $module;

    public function __construct()
    {
        $this->userRepository = app(UserRepository::class);
        $this->moduleRepository = app(ModuleRepository::class);
    }

    /**
     * @param string $module
     * @param bool $throwException
     * 
     * @return bool
     */
    public function authenticate($module = '', $throwException = true): bool
    {
        if($module != '') {
            $this->module = $module;
        }
        $user = $this->userRepository->currentUser();
        if($user['role'] == User::ROLES['COMPANY_ADMIN']) {
            return true;
        }

        $module = $this->moduleRepository->getModule($this->module);

        if(!in_array($module->module_code, $user['permitted_modules'])) {
            throw new AuthenticationException();
        }

        return true;
    }
}