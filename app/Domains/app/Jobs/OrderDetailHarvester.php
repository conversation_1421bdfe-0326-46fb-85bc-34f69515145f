<?php

namespace Main\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

use Main\Services\ShopService;

class OrderDetailHarvester implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var ShopService
     */
    protected ShopService $service;

    protected array $integrationData;

    protected int $orderNumber;

    /**
     * Create a new job instance.
     *
     * @return void
     * 
     * @param ShopService $service
     */
    public function __construct(array $integrationData, int $orderNumber)
    {
        $this->integrationData = $integrationData;
        $this->service = new ShopService($this->integrationData);
        $this->orderNumber = $orderNumber;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $this->service->orderDetails($this->orderNumber);
        } catch(\Exception $e) {
            Log::channel('cron')->withContext($this->integrationData)->error($e->getMessage());
        }
    }

    public function failed($e)
    {
        Log::channel('cron')->error($e->getMessage());
    }
}