<?php

namespace Main\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use App\Jobs\CompanyMonthlyInvoiceJob;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Main\Modules\Company\Models\Company;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Main\Modules\Transaction\Services\CreateMonthlyInvoiceService;



class CreateMonthlyInvoiceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected CreateMonthlyInvoiceService $service;
    public int $timeout = 86400;
    public function __construct()
    {
        $this->service = new CreateMonthlyInvoiceService();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            if (date('j') == 1) {
                Company::with('merchant', 'country')->whereHas('invoice', function ($query) {
                    $query->whereMonth('invoices.created_at', now()->subMonth()->month)->where('transaction_from', 'BALANCE');
                })->chunk(1, function ($company) {
                    CompanyMonthlyInvoiceJob::dispatch($company)->onQueue('monthly-invoice');
                });
            }
        } catch (\Exception $e) {
            Log::channel('cron')->error($e->getMessage());
        }
    }

    public function failed($e)
    {
        Log::channel('cron')->error($e->getMessage());
    }
}
