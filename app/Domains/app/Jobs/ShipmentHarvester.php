<?php

namespace Main\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Main\Modules\Shipment\Models\Shipment;
use Main\Services\ShipmentService;
use Main\Services\ShopService;

class ShipmentHarvester implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected ShipmentService $service;

    /**
     * Create a new job instance.
     *
     * @return void
     *
     * @param ShopService $service
     */
    public function __construct()
    {
        $this->service = new ShipmentService();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // try {
        // $this->service->updateShipments();
        $this->service->updateLatestShipmentEvents();
        // } catch (\Exception $e) {
        //     Log::info('shipment harvester exception');
        //     Log::channel('cron')->error($e->getMessage());
        // }
    }

    public function failed($e)
    {
        Log::channel('cron')->error($e->getMessage());
    }
}
