<?php

namespace Main\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Main\Modules\Order\Models\Customer;

class NotifyCustomerEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */

    public int $orderCode;
    public string $orderNumber;
    public int $customerCode;
    public string $email;
    public string $customerName;
    public string $phoneNumber;
    public string $eventHook;
    public Customer $customer;

    public function __construct(int $orderCode, string $orderNumber, Customer $customer, string $eventHook)
    {
        $this->orderCode = $orderCode;
        $this->orderNumber = $orderNumber;
        $this->customer = $customer;
        $this->eventHook = $eventHook;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}