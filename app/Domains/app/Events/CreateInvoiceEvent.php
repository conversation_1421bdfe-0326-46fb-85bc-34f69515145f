<?php

namespace Main\Events;

use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;
use Main\Modules\Order\Models\OrderRefund;

class CreateInvoiceEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */

    public int $orderCode;
    public array $invoiceItems;
    public array $discount;
    public float $tax;
    public string $invoiceType;
    public array $shippingLines;
    public array $customerInfo;
    public $orderRefund;

    public function __construct(
        int $orderCode,
        array $invoiceItems,
        array $discount,
        string $invoiceType,
        array $customerInfo = [],
        array $shippingLines = [],
        array $orderRefund = []
    ) {
        $this->orderCode = $orderCode;
        $this->invoiceItems = $invoiceItems;
        $this->tax = $invoiceItems[0]['vat_rate'] ?? 0.00;
        $this->discount = $discount;
        $this->invoiceType = $invoiceType;
        $this->customerInfo = $customerInfo;
        $this->shippingLines = $shippingLines ?? [];
        $this->orderRefund = $orderRefund;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
