<?php

namespace Main\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SaveOrderEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */

    public string $subject;
    public string $verb;
    public string $message;
    public string $description;
    public int $orderCode;
    public $event_id;

    public function __construct(int $orderCode, string $subject, string $verb, string $message, string $description, $event_id = null)
    {
        $this->orderCode = $orderCode;
        $this->subject = $subject;
        $this->verb = $verb;
        $this->message = $message;
        $this->description = $description;
        $this->event_id = $event_id;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
