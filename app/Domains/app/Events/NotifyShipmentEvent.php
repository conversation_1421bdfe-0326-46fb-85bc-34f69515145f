<?php

namespace Main\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Main\Modules\Shipment\Models\Shipment;

class NotifyShipmentEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */

    public Shipment $shipment;
    public string $eventHook;

    public function __construct(Shipment $shipment, string $eventHook)
    {
        $this->shipment = $shipment;
        $this->eventHook = $eventHook;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
