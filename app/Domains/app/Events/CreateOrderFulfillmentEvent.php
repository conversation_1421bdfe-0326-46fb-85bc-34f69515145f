<?php

namespace Main\Events;

use Main\Modules\Order\Models\Order;
use Illuminate\Queue\SerializesModels;
use Main\Modules\Company\Models\Company;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;

class CreateOrderFulfillmentEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Order $order;
    public string $eventHook;
    public Company $company;

    // public function __construct(Order $order, string $eventHook)
    public function __construct(Order $order, string $eventHook,Company $company)
    {
        $this->order = $order;
        $this->eventHook = $eventHook;
        $this->company = $company;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
