<?php

namespace Main\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Main\Modules\Company\Models\Company;
use Main\Modules\Shipment\Models\Shipment;

class ShopifyOrderShipmentEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public Shipment $shipment;
    public string $eventHook;
    public Company $company;

    public function __construct(Shipment $shipment, string $eventHook,Company $company)
    {
        $this->shipment = $shipment;
        $this->eventHook = $eventHook;
        $this->company = $company;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
