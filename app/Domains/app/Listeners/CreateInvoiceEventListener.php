<?php

namespace Main\Listeners;

use Illuminate\Support\Facades\Auth;
use Main\Events\CreateInvoiceEvent;
use Main\Events\SaveOrderEvent;
use Main\Modules\Order\Services\SalesInvoiceService;

class CreateInvoiceEventListener
{

    public SalesInvoiceService $salesInvoiceService;

    public function __construct(SalesInvoiceService $salesInvoiceService)
    {
        $this->salesInvoiceService = $salesInvoiceService;
    }

    public function handle(CreateInvoiceEvent $event)
    {
        $invoice = $this->salesInvoiceService->createInvoice(
            $event->orderCode,
            $event->invoiceItems,
            $event->discount,
            $event->invoiceType,
            $event->customerInfo,
            $event->shippingLines,
            $event->orderRefund
        );

        if ($invoice) {
            event(
                new SaveOrderEvent(
                    $event->orderCode,
                    'Order',
                    'invoice_created',
                    (Auth::user()->company->company_name ?? 'Shopify Admin') . ' created invoice',
                    (Auth::user()->company->company_name ?? 'Shopify Admin') . ' created invoice'
                )
            );
        }
    }
}
