<?php

namespace Main\Listeners;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Main\Modules\Order\Models\Customer;
use App\Notifications\NotifyCustomerSMS;
use Main\Modules\Company\Models\Company;
use App\Notifications\NotifyCustomerEmail;
use Main\Events\ShopifyOrderShipmentEvent;
use Main\Modules\Setting\Entities\MessageTemplate;
use Main\Modules\Shipment\Entities\AdditionalService;
use Main\Modules\Setting\Repositories\MessageHookRepository;
use Main\Modules\Setting\Repositories\MessageTemplateRepository;
use Main\Modules\Company\Repositories\PersonalizedMessageRepository;
use Main\Modules\Platforms\Models\Integration;

class ShopifyOrderShipmentEventlistener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    private int $companyCode;
    private string $eventHook;
    private string $recipientName;
    private string $recipientEmail;
    private string $recipientPhone;
    private string $receiverCountryCode;
    private string $shippingAgent;
    private string $shippingProduct;
    private int $parcelCount;
    private $reference;
    private $integration_code;
    private $integration_name;
    private string $parcelShopID;
    private string $trackingUrl;
    private string $trackingCode;
    private string $carrier;
    private string $footer;
    private Customer $customer;
    private Company $company;

    private MessageTemplateRepository $messageTemplateRepository;
    private MessageHookRepository $messageHookRepository;
    private PersonalizedMessageRepository $personalizedMessageRepository;

    public function __construct()
    {
        $this->messageHookRepository = app(\Main\Modules\Setting\Repositories\MessageHookRepository::class);
        $this->messageTemplateRepository = app(\Main\Modules\Setting\Repositories\MessageTemplateRepository::class);
        $this->personalizedMessageRepository = app(\Main\Modules\Company\Repositories\PersonalizedMessageRepository::class);
        $this->companyCode = Auth::user()->company_code;
    }

    public function handle(ShopifyOrderShipmentEvent $event)
    {
        $this->company = $event->company;
        if (!$event->shipment->recipient['contactInfo']['email']) {
            return;
        }

        $this->eventHook = $event->eventHook;

        $this->recipientName = $event->shipment->recipient['firstName'] . ' ' . $event->shipment->recipient['lastName'];
        $this->recipientEmail = $event->shipment->recipient['contactInfo']['email'];

        if (!isset($event->shipment->recipient['contactInfo']['telephone'])) {
            $this->recipientPhone = $event->shipment->recipient['contactInfo']['dialCode'] ?? null;
            $this->recipientPhone .= $event->shipment->recipient['contactInfo']['telephone'];
        } else {
            $this->recipientPhone = $event->shipment->recipient['contactInfo']['telephone'];
        }
        $this->integration_code = $event->company->personalizedMessage[0]->integration_code;
        $this->integration_name = $event->company->personalizedMessage[0]['integration']->name ?? $event->shipment->order->integration ?? 'N/A';

        $this->receiverCountryCode = $event->shipment->receiverCountry->country_code;
        $this->shippingAgent = $event->shipment->carrierProduct->carrier->carrier_id;
        $this->carrier = $event->shipment->carrierProduct->carrier->name;
        $this->shippingProduct = $event->shipment->carrierProduct->product_id;
        $this->parcelCount = count($event->shipment->parcels);
        $this->reference = $event->shipment->carrier_reference;
        $this->parcelShopID = '';

        $customer = Customer::where(
            [
                ['email', '=', $this->recipientEmail],
                ['company_code', '=', $this->companyCode],
            ]
        )
            ->first();

        if (!$customer) {
            $this->customer = Customer::create(
                [
                    'customer_code' => generateHash(),
                    'first_name' => $event->shipment->recipient['firstName'],
                    'last_name' => $event->shipment->recipient['lastName'],
                    'email' => $this->recipientEmail,
                    'phone' => $this->recipientPhone,
                    'company_code' => $this->companyCode
                ]
            );
        } else {
            $this->customer = $customer;
        }

        $this->trackingCode = $event->shipment->parcels[0]['carrierReference'] ?? '';
        $additionalServices = $event->shipment->additional_services;

        $asChargeRepository = app(\Main\Modules\Shipment\Repositories\ASChargeRepository::class);
        $getFooterTemplateDeatil = DB::table('sa_template')->where('type', 'FOOTER')->first();
        $this->footer = ($getFooterTemplateDeatil->summary) ? $getFooterTemplateDeatil->summary : '';
        $this->trackingUrl = $event->shipment->carrierProduct->carrier->tracking_url;

        if ($additionalServices != null) {
            foreach ($additionalServices as $service) {
                $serviceDetail = $asChargeRepository->asCharge($service);
                if ($serviceDetail->additionalService->notification_type == AdditionalService::NOTIFICATION_TYPE['EMAIL']) {
                    if ($serviceDetail->additionalService->is_personalized == 1) {
                        $this->sendPersonalizedEmailNotification();
                    }
                }
            }
        }
    }

    private function sendPersonalizedEmailNotification(): void
    {
        if (!$this->company['personalizedMessage']) {
            return;
        }
        $mailContent = $this->prepareMessage($this->company['personalizedMessage'][0]->message_content);
        $subject = $this->prepareMessage($this->company['personalizedMessage'][0]->subject);
        if ($this->company['personalizedMessage'][0]->send_time != null) {
            $this->customer->notify(
                new NotifyCustomerEmail(
                    $this->recipientName,
                    $mailContent,
                    $subject,
                    $this->company['personalizedMessage'][0]->bcc,
                    $this->integration_name
                )
            );
        } else {
            $this->customer->notify(
                new NotifyCustomerEmail(
                    $this->recipientName,
                    $mailContent,
                    $subject,
                    $this->company['personalizedMessage'][0]->bcc,
                    $this->integration_name
                )
            );
        }
    }

    private function sendEmailNotification(): void
    {
        $emailMessageModel = $this->messageTemplateRepository->getMessage($this->eventHook, MessageTemplate::TEMPLATE_TYPE['EMAIL']);

        if (!$emailMessageModel) {
            return;
        }

        $mailContent = $this->prepareMessage($emailMessageModel->message_content);
        $subject = $this->prepareMessage($emailMessageModel->subject);

        if ($emailMessageModel->send_time != null) {
            $this->customer->notify(
                new NotifyCustomerEmail(
                    $this->recipientName,
                    $mailContent,
                    $subject,
                    $emailMessageModel->bcc,
                    $this->integration_name
                )
            );
        } else {
            $this->customer->notify(
                new NotifyCustomerEmail(
                    $this->recipientName,
                    $mailContent,
                    $subject,
                    $emailMessageModel->bcc,
                    $this->integration_name
                )
            );
        }
    }

    private function sendPersonalizedSMSNotification(): void
    {
        $emailMessageModel = $this->personalizedMessageRepository->personalizedMessage($this->eventHook, $this->companyCode, MessageTemplate::TEMPLATE_TYPE['SMS'], $this->receiverCountryCode);
        if (!$emailMessageModel) {
            return;
        }

        $mailContent = $this->prepareMessage($emailMessageModel->message_content);
        if ($emailMessageModel->send_time != null) {
            $this->customer->notify(new NotifyCustomerSMS($mailContent));
        } else {
            $this->customer->notify(new NotifyCustomerSMS($mailContent));
        }
    }

    private function sendSMSNotification(): void
    {
        $emailMessageModel = $this->messageTemplateRepository->getMessage($this->eventHook, MessageTemplate::TEMPLATE_TYPE['SMS']);
        if (!$emailMessageModel) {
            return;
        }

        $mailContent = $this->prepareMessage($emailMessageModel->message_content);
        if ($emailMessageModel->send_time != null) {
            $this->customer->notify(new NotifyCustomerSMS($mailContent));
        } else {
            $this->customer->notify(new NotifyCustomerSMS($mailContent));
        }
    }

    private function prepareMessage(string $messageTemplate): string
    {
        $messageText = $messageTemplate;
        $arTags = [];
        $arReplacements = [];
        preg_match_all('/\{{(.*)\}}/', $messageTemplate, $matches);
        if (isset($matches[0]) && count($matches[0]) > 0) {
            foreach ($matches[0] as $match) {
                // dump('match',$match);
                $search = [
                    '{{company_name}}',
                    '{{sender_email}}',
                    '{{receiver_name}}',
                    '{{receiver_email}}',
                    '{{receiver_country_code}}',
                    '{{receiver_mobile}}',
                    '{{tt}}',
                    '{{tt_url}}',
                    '{{tt_link}}',
                    '{{carrier}}',
                    '{{parcel_numbers}}',
                    '{{parcel_count}}',
                    '{{shipping_agent}}',
                    '{{shipping_product}}',
                    '{{service_point}}',
                    '{{trackshipment}}'
                ];

                $replace = [
                    Auth::user()->company->company_name,
                    Auth::user()->email,
                    $this->recipientName,
                    $this->recipientEmail,
                    $this->receiverCountryCode,
                    $this->recipientPhone,
                    $this->trackingCode,
                    $this->trackingUrl,
                    'Track Your Shipment',
                    $this->carrier,
                    '',
                    $this->parcelCount,
                    $this->shippingAgent,
                    $this->shippingProduct,
                    $this->parcelShopID,
                    '<a href="' . $this->trackingUrl . $this->trackingCode . '">' . $this->trackingCode . '</a>'
                    // '<a href="'.$this->trackingUrl.'">'.$this->reference.'</a>'
                ];
            }
            $messageText = str_replace($search, $replace, $messageTemplate);
        }
        return $messageText;
    }
}
