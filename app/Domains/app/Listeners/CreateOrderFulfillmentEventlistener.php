<?php

namespace Main\Listeners;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Main\Modules\Order\Models\Customer;
use App\Notifications\NotifyCustomerSMS;
use Main\Modules\Company\Models\Company;
use App\Notifications\NotifyCustomerEmail;
use Main\Modules\Setting\Entities\MessageTemplate;
use Main\Modules\Shipment\Entities\AdditionalService;
use Main\Modules\Setting\Repositories\MessageHookRepository;
use Main\Modules\Setting\Repositories\MessageTemplateRepository;
use Main\Modules\Company\Repositories\PersonalizedMessageRepository;
use Main\Events\CreateOrderFulfillmentEvent as EventsCreateOrderFulfillmentEvent;

class CreateOrderFulfillmentEventlistener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    private int $companyCode;
    private string $eventHook;
    private string $recipientName;
    private string $recipientEmail;
    private string $recipientPhone;
    private string $receiverCountryCode;
    private string $shippingAgent;
    private string $shippingProduct;
    private int $parcelCount;
    private $reference;
    private $integration_code;
    private $integration_name;
    private string $parcelShopID;
    private string $trackingUrl;
    private string $trackingCode;
    private string $carrier;
    private string $footer;
    private Customer $customer;
    private Company $company;
    private string $order_number;
    private mixed $order_name;

    private MessageTemplateRepository $messageTemplateRepository;
    private MessageHookRepository $messageHookRepository;
    private PersonalizedMessageRepository $personalizedMessageRepository;

    public function __construct()
    {
        $this->messageHookRepository = app(\Main\Modules\Setting\Repositories\MessageHookRepository::class);
        $this->messageTemplateRepository = app(\Main\Modules\Setting\Repositories\MessageTemplateRepository::class);
        $this->personalizedMessageRepository = app(\Main\Modules\Company\Repositories\PersonalizedMessageRepository::class);
        $this->companyCode = Auth::user()->company_code;
    }

    public function handle(EventsCreateOrderFulfillmentEvent $event)
    {
        $this->company = $event->company;
        if (!$event->order['shipping_address']['email']) {
            return;
        }

        $this->eventHook = $event->eventHook;
        $this->recipientName = $event->order['shipping_address']['name'];
        $this->recipientEmail = $event->order['shipping_address']['email'];
        $this->recipientPhone = $event->order['shipping_address']['phone'] ?? '';
        $this->integration_code = $event->company->personalizedMessage[0]->integration_code;
        $this->integration_name = $event->order->integration->name ?? 'Integration';
        $this->receiverCountryCode = $event->order['shipping_address']['country_code'];
        $this->carrier = $event->order->deliveryTemplate->carrierProduct->carrier->name;

        $this->order_number = $event->order->order_number;
        $this->order_name = $event->order->name ?? '';
        $customer = Customer::where([
            ['email', '=', $this->recipientEmail],
            ['company_code', '=', $this->companyCode],
        ])->first();

        if (!$customer) {
            $splitName = explode(' ', $event->order['shipping_address']['name'], 2);
            $first_name = $splitName[0];
            $last_name = !empty($splitName[1]) ? $splitName[1] : '';
            $this->customer = Customer::create(
                [
                    'customer_code' => generateHash(),
                    'first_name' => $first_name,
                    'last_name' => $last_name,
                    'email' => $this->recipientEmail,
                    'phone' => $this->recipientPhone,
                    'company_code' => $this->companyCode
                ]
            );
        } else {
            $this->customer = $customer;
        }

        // $this->trackingUrl = '#';
        // if ($event->order->carrierProduct->carrier->tracking_url != null && isset($event->order->parcels[0]['carrierReference'])) {
        //     $this->trackingUrl = $event->order->carrierProduct->carrier->tracking_url . $event->order->parcels[0]['carrierReference'];
        // }

        // $this->trackingCode = $event->order->parcels[0]['carrierReference'] ?? '';

        $additionalServices = $event->order->additional_services;

        $asChargeRepository = app(\Main\Modules\Shipment\Repositories\ASChargeRepository::class);
        $getFooterTemplateDeatil = DB::table('sa_template')->where('type', 'FOOTER')->first();
        $this->footer = ($getFooterTemplateDeatil->summary) ? $getFooterTemplateDeatil->summary : '';

        if ($additionalServices != null) {
            foreach ($additionalServices as $service) {
                $serviceDetail = $asChargeRepository->asCharge($service);
                if ($serviceDetail->additionalService->notification_type == AdditionalService::NOTIFICATION_TYPE['EMAIL']) {
                    if ($serviceDetail->additionalService->is_personalized == 1) {
                        /** this was commented before by user Ubuntu */
                        $this->sendPersonalizedEmailNotification();
                    }
                }
            }
        }
    }

    private function sendPersonalizedEmailNotification(): void
    {
        if (!$this->company['personalizedMessage']) {
            return;
        }
        $mailContent = $this->prepareMessage($this->company['personalizedMessage'][0]->message_content);
        $subject = $this->prepareMessage($this->company['personalizedMessage'][0]->subject);
        if ($this->company['personalizedMessage'][0]->send_time != null) {
            info("creating jobs for fulfillment sending email");
            $this->customer->notify(
                new NotifyCustomerEmail(
                    $this->recipientName,
                    $mailContent,
                    $subject,
                    $this->company['personalizedMessage'][0]->bcc,
                    $this->integration_name
                )
            );
        } else {
            $this->customer->notify(
                new NotifyCustomerEmail(
                    $this->recipientName,
                    $mailContent,
                    $subject,
                    $this->company['personalizedMessage'][0]->bcc,
                    $this->integration_name
                )
            );
        }
    }

    private function sendEmailNotification(): void
    {
        $emailMessageModel = $this->messageTemplateRepository->getMessage($this->eventHook, MessageTemplate::TEMPLATE_TYPE['EMAIL']);

        if (!$emailMessageModel) {
            return;
        }

        $mailContent = $this->prepareMessage($emailMessageModel->message_content);
        $subject = $this->prepareMessage($emailMessageModel->subject);

        if ($emailMessageModel->send_time != null) {
            $this->customer->notify(
                new NotifyCustomerEmail(
                    $this->recipientName,
                    $mailContent,
                    $subject,
                    $this->company['personalizedMessage'][0]->bcc,
                    $this->integration_name
                )
            );
        } else {
            $this->customer->notify(
                new NotifyCustomerEmail(
                    $this->recipientName,
                    $mailContent,
                    $subject,
                    $this->company['personalizedMessage'][0]->bcc,
                    $this->integration_name
                )
            );
        }
    }

    private function sendPersonalizedSMSNotification(): void
    {
        $emailMessageModel = $this->personalizedMessageRepository->personalizedMessage($this->eventHook, $this->companyCode, MessageTemplate::TEMPLATE_TYPE['SMS'], $this->receiverCountryCode);


        if (!$emailMessageModel) {
            return;
        }

        $mailContent = $this->prepareMessage($emailMessageModel->message_content);

        if ($emailMessageModel->send_time != null) {
            $this->customer->notify(new NotifyCustomerSMS($mailContent));
        } else {
            $this->customer->notify(new NotifyCustomerSMS($mailContent));
        }
    }

    private function sendSMSNotification(): void
    {
        $emailMessageModel = $this->messageTemplateRepository->getMessage($this->eventHook, MessageTemplate::TEMPLATE_TYPE['SMS']);

        if (!$emailMessageModel) {
            return;
        }

        $mailContent = $this->prepareMessage($emailMessageModel->message_content);

        if ($emailMessageModel->send_time != null) {
            $this->customer->notify(new NotifyCustomerSMS($mailContent));
        } else {
            $this->customer->notify(new NotifyCustomerSMS($mailContent));
        }
    }

    private function prepareMessage(string $messageTemplate): string
    {
        $messageText = $messageTemplate;
        $arTags = [];
        $arReplacements = [];
        preg_match_all('/\{{(.*)\}}/', $messageTemplate, $matches);
        if (isset($matches[0]) && count($matches[0]) > 0) {
            foreach ($matches[0] as $match) {
                // dump('match',$match);
                $search = [
                    '{{company_name}}',
                    // '{{sender_email}}',
                    '{{receiver_name}}',
                    '{{order_number}}',
                    // '{{receiver_country_code}}',
                    // '{{receiver_mobile}}',
                    // '{{tt}}',
                    // '{{tt_url}}',
                    // '{{tt_link}}',
                    '{{carrier}}',
                    // '{{parcel_numbers}}',
                    // '{{parcel_count}}',
                    // '{{shipping_agent}}',
                    // '{{shipping_product}}',
                    // '{{service_point}}',
                    // '{{trackshipment}}'
                ];

                $replace = [
                    Auth::user()->company->company_name,
                    // Auth::user()->email,
                    $this->recipientName,
                    $this->order_name ?? $this->order_number,
                    // $this->receiverCountryCode,
                    // $this->recipientPhone,
                    // $this->trackingCode,
                    // $this->trackingUrl,
                    // 'Track Your Shipment',
                    $this->carrier,
                    // '',
                    // $this->parcelCount,
                    // $this->shippingAgent,
                    // $this->shippingProduct,
                    // $this->parcelShopID,
                    // '<a href="'.$this->trackingUrl.'">'.$this->trackingCode.'</a>'
                    // '<a href="'.$this->trackingUrl.'">'.$this->reference.'</a>'
                ];
            }
            $messageText = str_replace($search, $replace, $messageTemplate);
        }

        return $messageText;
    }
}
