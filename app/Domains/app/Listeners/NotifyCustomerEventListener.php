<?php

namespace Main\Listeners;

use App\Notifications\NotifyCustomerEmail;
use App\Notifications\NotifyCustomerSMS;
use Illuminate\Support\Facades\Auth;
use Main\Events\NotifyCustomerEvent;
use Main\Modules\Company\Repositories\PersonalizedMessageRepository;
use Main\Modules\Order\Models\Customer;
use Main\Modules\Setting\Entities\MessageTemplate;
use Main\Modules\Setting\Repositories\MessageTemplateRepository;

class NotifyCustomerEventListener
{
    public int $orderCode;
    public int $orderNumber;
    public int $companyCode;
    public Customer $customer;
    public string $eventHook;

    private MessageTemplateRepository $messageTemplateRepository;
    private PersonalizedMessageRepository $personalizedMessageRepository;


    public function __construct()
    {
        $this->messageTemplateRepository = app(\Main\Modules\Setting\Repositories\MessageTemplateRepository::class);

        $this->personalizedMessageRepository = app(\Main\Modules\Company\Repositories\PersonalizedMessageRepository::class);
        $this->companyCode = Auth::user()->company_code;
    }

    public function handle(NotifyCustomerEvent $event)
    {
        $this->orderCode = $event->orderCode;
        $this->orderNumber = $event->orderNumber;
        $this->customer = $event->customer;
        $this->eventHook = $event->eventHook;

        // $this->sendEmailNotification();
        if ($this->customer->phone != '') {
            $this->sendSMSNotification();
        }
    }

    private function sendEmailNotification(): void
    {
        $emailMessageModel = null;
        if ($this->customer != null) {
            $emailMessageModel = $this->personalizedMessageRepository->personalizedMessage($this->eventHook, $this->companyCode, MessageTemplate::TEMPLATE_TYPE['EMAIL'], $this->customer->country_code, $this->customer->integration_code);
        }

        if (!$emailMessageModel) {
            // get default message
            $emailMessageModel = $this->messageTemplateRepository->getMessage($this->eventHook, MessageTemplate::TEMPLATE_TYPE['EMAIL']);
        }

        $mailContent = $this->prepareMessage($emailMessageModel->message_content);
        $subject = $this->prepareMessage($emailMessageModel->subject);

        if ($emailMessageModel->send_time != null) {
            $this->customer->notify(
                new NotifyCustomerEmail(
                    '',
                    $mailContent,
                    $subject,
                    $emailMessageModel->bcc,
                    'Shipvagoo'
                )
            );
        } else {
            $this->customer->notify(
                new NotifyCustomerEmail(
                    '',
                    $mailContent,
                    $subject,
                    $emailMessageModel->bcc,
                    'Shipvagoo'
                )
            );
        }
    }

    private function sendSMSNotification(): void
    {
        $emailMessageModel = null;
        if ($this->customer != null) {
            $emailMessageModel = $this->personalizedMessageRepository->personalizedMessage($this->eventHook, $this->companyCode, MessageTemplate::TEMPLATE_TYPE['SMS'], $this->customer->country_code, $this->customer->integration_code);
        }

        if (!$emailMessageModel) {
            $emailMessageModel = $this->messageTemplateRepository->getMessage($this->eventHook, MessageTemplate::TEMPLATE_TYPE['SMS']);
        }

        $mailContent = $this->prepareMessage($emailMessageModel->message_content);

        if ($emailMessageModel->send_time != null) {
            $this->customer->notify(new NotifyCustomerSMS($mailContent));
        } else {
            $this->customer->notify(new NotifyCustomerSMS($mailContent));
        }
    }

    private function prepareMessage(string $messageTemplate): string
    {
        $messageText = '';

        $arTags = [];
        $arReplacements = [];
        preg_match_all('/\*(.*)\*/', $messageTemplate, $matches);
        if (isset($matches[0]) && count($matches[0]) > 0) {
            foreach ($matches[0] as $match) {
                switch ($match) {
                    case '*sender_name*':
                        $arTags[] = $match;
                        $arReplacements[] = Auth::user()->company->company_name;
                        break;
                    case '*sender_email*':
                        $arTags[] = $match;
                        $arReplacements[] = Auth::user()->email;
                        break;
                    case '*receiver_name*':
                        $arTags[] = $match;
                        $arReplacements[] = $this->customer->first_name . ' ' . $this->customer->last_name;
                        break;
                    case '*receiver_email*':
                        $arTags[] = $match;
                        $arReplacements[] = $this->customer->email;
                        break;
                    case '*receiver_country_code*':
                        $arTags[] = $match;
                        $arReplacements[] = '';
                        break;
                    case '*receiver_mobile*':
                        $arTags[] = $match;
                        $arReplacements[] = $this->customer->phone;
                        break;
                    case '*tt*':
                        $arTags[] = $match;
                        $arReplacements[] = '';
                        break;
                    case '*tt_url*':
                        $arTags[] = $match;
                        $arReplacements[] = '';
                        break;
                    case '*tt_link*':
                        $arTags[] = $match;
                        $arReplacements[] = '';
                        break;
                    case '*parcel_numbers*':
                        $arTags[] = $match;
                        $arReplacements[] = '';
                        break;
                    case '*parcel_count*':
                        $arTags[] = $match;
                        $arReplacements[] = '';
                        break;
                    case '*shipping_agent*':
                        $arTags[] = $match;
                        $arReplacements[] = '';
                        break;
                    case '*shipping_product*':
                        $arTags[] = $match;
                        $arReplacements[] = '';
                        break;
                    case '*service_point*':
                        $arTags[] = $match;
                        $arReplacements[] = '';
                        break;
                    case '*reference*':
                        $arTags[] = $match;
                        $arReplacements[] = '';
                        break;
                    case '*order_id*':
                        $arTags[] = $match;
                        $arReplacements[] = $this->orderNumber;
                        break;
                    default:
                        break;
                }
            }
            $messageText = str_replace($arTags, $arReplacements, $messageTemplate);
        }
        return $messageText;
    }
}
