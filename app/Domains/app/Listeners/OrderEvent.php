<?php

namespace Main\Listeners;

use Illuminate\Support\Facades\Auth;
use Main\Events\SaveOrderEvent;
use Main\Modules\Order\Services\EventService;

class OrderEvent
{

    public EventService $service;

    public function __construct(EventService $service)
    {
        $this->service = $service;
    }

    public function handle(SaveOrderEvent $event)
    {
        $this->service->createEvent(
            [
                'order_code' => $event->orderCode,
                'subject_type' => $event->subject,
                'verb' => $event->verb,
                'author' => Auth::check() ? Auth::user()->company->company_name : 'Shopify Admin',
                'message' => $event->message,
                'description' => $event->description,
                'event_id' => $event->event_id
            ]
        );
    }
}
