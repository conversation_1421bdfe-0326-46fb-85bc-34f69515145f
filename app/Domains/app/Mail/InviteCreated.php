<?php

namespace Main\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Auth;

class InviteCreated extends Mailable
{
    use Queueable, SerializesModels;

    public $email;
    public $name;
    public $token;
    public $companyName;
    public $inviteCode;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($name, $email, $token, $inviteCode)
    {
        //
        $this->name = $name;
        $this->email = $email;
        $this->token = $token;
        $this->companyName = Auth::user()->company->company_name;
        $this->inviteCode = $inviteCode;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this
            ->to($this->email)
            ->view('mail.invite')
            ->subject('Staff User Created at '.config('app.name'));
    }
}
