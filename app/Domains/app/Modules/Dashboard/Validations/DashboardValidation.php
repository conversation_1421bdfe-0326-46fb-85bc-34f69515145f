<?php

namespace Main\Modules\Dashboard\Validations;

use Illuminate\Validation\Rule;

use Core\Validation\AbstractValidation;
use Core\Contracts\Validation\Validation;

class DashboardValidation extends AbstractValidation
{
    public function store(): Validation
    {
        $this->rules = [];

        return $this;
    }

    public function update(): Validation
    {
        $this->rules = [];
        
        return $this;
    }
    
    public function overViewChart(): Validation
    {
        $this->rules = [
            'days'=>[
                'required',
                'numeric',
                Rule::in([7,14,21,28])
            ]
        ];

        return $this;
    }
}