<?php

namespace Main\Modules\Dashboard\Controllers;

use Core\Controllers\AbstractController;
use Illuminate\Http\Request;
use Main\Modules\Dashboard\Requests\DashboardRequest;
use Main\Modules\Dashboard\Services\DashboardService;

class DashboardController extends AbstractController
{
    /**
     * @param DocumentService $service
     */
    public function __construct(DashboardService $service, DashboardRequest $request)
    {
        $this->domainService = $service;
        $this->domainRequest = $request;
    }

    public function bookedShipments()
    {
        $response = $this->domainService->bookedShipment();
        return response()->json($response->getData(), $response->code());
    }

    public function integrationIssues()
    {
        $response = $this->domainService->integrationIssues();
        return response()->json($response->getData(), $response->code());
    }

    public function overview(Request $request)
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->overview($this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }

    public function orderStats(Request $request)
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->orderStats($this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }

    public function openTasks()
    {
        $response = $this->domainService->openTasks();
        return response()->json($response->getData(), $response->code());
    }

    public function salesStats(Request $request)
    {
        $this->domainRequest->set($request->all());

        $response = $this->domainService->salesStats($this->domainRequest);

        return response()->json($response->getData(), $response->code());
    }
}
