<?php

declare(strict_types=1);

namespace Main\Modules\Dashboard\Services;

use Carbon\Carbon;
use Core\Contracts\Request\Request;

use Core\Contracts\Response\Response;
use Core\Exceptions\ValidationException;
use Core\Services\AbstractService;
use Illuminate\Support\Facades\Auth;
use Main\Modules\Dashboard\Response\DashboardResponse;
use Main\Modules\Dashboard\Validations\DashboardValidation;
use Main\Modules\Order\Entities\Order;
use Main\Modules\Platforms\Repositories\IntegrationRepository;
use Main\Modules\Platforms\Repositories\DeliveryMethodRepository;
use Main\Modules\Shipment\Entities\Shipment;
use Main\Modules\Shipment\Repositories\ShipmentRepository;
use Main\Modules\Order\Repositories\OrderRepository;
use Main\Modules\Order\Repositories\SalesInvoiceRepository;
use Main\Modules\Transaction\Repositories\InvoiceRepository;

class DashboardService extends AbstractService
{
    public function __construct(
        DashboardValidation $validator,
        DashboardResponse $response
    ) {
        $this->validator = $validator;
        $this->response = $response;
    }

    public function bookedShipments(): Response
    {
        $shipmentRepository = app(\Main\Modules\Shipment\Repositories\ShipmentRepository::class);
        $response = $shipmentRepository->getShipments(Shipment::SHIPMENT_STATUS['REGISTERED']);
        $this->response->setData($response->toArray());
        return $this->response;
    }

    public function integrationIssues(): Response
    {
        /** @var IntegrationRepository $integrationRepository */
        $integrationRepository = app(\Main\Modules\Platforms\Repositories\IntegrationRepository::class);
        /** @var DeliveryMethodRepository $deliveryMethodRepository */
        $deliveryMethodRepository = app(\Main\Modules\Platforms\Repositories\DeliveryMethodRepository::class);

        $inactiveIntegrations = $integrationRepository->inactiveIntegrations();
        $deliveryMethodsNeedSetup = $deliveryMethodRepository->getDeliveryMethodsNeedSetupCount();

        $response = [
            'inactive_integrations' => $inactiveIntegrations ?? 0,
            'delivery_methods_need_setup' => $deliveryMethodsNeedSetup ?? 0,
        ];

        $this->response->setData($response);
        return $this->response;
    }

    public function overview(Request $request): Response
    {
        $arData = [];

        try {
            $companyCode = Auth::user()->company_code;
            $this->validator->overViewChart()->validate($request);
            $validated = $this->validator->validated();

            /** @var ShipmentRepository $shipmentRepository */
            $shipmentRepository = app(ShipmentRepository::class);
            $response = $shipmentRepository->getShipmentCountByDay((int)$validated['days'], $companyCode);
            $arData['shipments'] = $response;

            /** @var OrderRepository $orderRepository */
            $orderRepository = app(OrderRepository::class);
            $arData['picked_orders'] = [];

            $response = $orderRepository->getOrderCountByDay((int)$validated['days'], $companyCode);
            $arData['imported_orders'] = $response;

            $arData['dispatched_orders'] = [];

            $this->response->setData($arData);
        } catch (ValidationException $e) {
            $this->response->setData(['error' => $e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch (\Exception $e) {
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode(400);
        }
        return $this->response;
    }

    public function orderStats(Request $request)
    {

        /** @var OrderRepository $orderRepository */
        $orderRepository = app(OrderRepository::class);

        // $totalOrders = $orderRepository->countOrdersWithFilter('OPEN');

        $totalOrders = $orderRepository->getUnfulfilledOrders();

        $ongoingOrders = $orderRepository->getfulfilledOrders();

        $orders = [
            'total_orders'  =>  $totalOrders,
            'ongoing_orders' => $ongoingOrders,
            'unfilled_return'   =>  0,
            'total_return'      =>  0,
        ];

        $this->response->setData($orders);

        return $this->response;
    }

    public function openTasks()
    {
        $statusCounts = \Main\Modules\Order\Models\Order::select('template_status', \DB::raw('COUNT(*) as count'))
            ->whereNot('template_status', 'existing')
            ->where('company_code', Auth::user()->company_code)
            ->groupBy('template_status')
            ->get();

        $openTasks = [];
        foreach ($statusCounts as $statusCount) {
            $description = "Around $statusCount->count " . \Str::plural('order', $statusCount->count) . " ";
            $description .= $statusCount->count == 1 ? 'has ' : 'have ';
            $description .= $statusCount->template_status == 'none' ? 'missing shipment template' : 'assigned ' . ($statusCount->template_status == 'created' ? 'newly system created delivery template' : 'manually set template from delivery methods');
            $openTasks[] = [
                'type'  =>  $statusCount->template_status == 'none' ? 'error' : 'warning',
                'label' => $statusCount->template_status,
                'description'   =>  $description
            ];
        }


        $this->response->setData($openTasks);

        return $this->response;
    }

    public function salesStats(Request $request)
    {
        $invoiceRepository = app(InvoiceRepository::class);
        $salesRepository = app(SalesInvoiceRepository::class);
        $orderRepository = app(OrderRepository::class);

        $invoice = $invoiceRepository->paidInvoiceWithShipments($request->get('filter'));
        $totalInvoiceCost = $this->calculateInvoicesCost($invoice->toArray());
        $weightClasses = $this->getWeightClassesStats($invoice->toArray(), $request->get('carriers'));

        $salesInvoices = $salesRepository->getCustomerSalesInvoices($request->get('filter'));
        $totalSalesInvoicesCost = $this->calculateSalesInvoicesCost($salesInvoices->toArray());
        $salesInvoicesCount = $salesInvoices->where('invoice_type', 'INVOICE')->count();
        $salesCreditNoteCount = $salesInvoices->where('invoice_type', 'CREDIT_NOTE')->count();
        $salesInvoicesCount = $salesInvoicesCount - $salesCreditNoteCount;

        //product count by carrier
        $shipmentRepository = app(\Main\Modules\Shipment\Repositories\ShipmentRepository::class);
        $shippingProducts = $shipmentRepository->getProductCountByCarrier($request->get('carriers'), $request->get('filter'));

        $topSellingProducts = $orderRepository->topSellingProducts($request->get('shop'), $request->get('filter'));

        if ($salesInvoicesCount > 0) {
            $avgSalePrice = number_format($totalSalesInvoicesCost / $salesInvoicesCount, 2);
        } else {
            $avgSalePrice = 0;
        }

        $sales = [
            'shipment_cost'         =>  $totalInvoiceCost,
            'total_shipments'       =>  $invoice->count(),
            'sales_invoice'         =>  number_format($totalSalesInvoicesCost, 2),
            'avg_sale_price'        =>  $avgSalePrice,
            'shipping_products'     =>  $shippingProducts,
            'weight_classes'        =>  $weightClasses,
            'top_selling_products'  =>  $topSellingProducts
        ];

        $this->response->setData($sales);

        return $this->response;
    }

    // $records = App\Record::whereBetween('created_at', [Carbon\Carbon::now()->startOfWeek(), Carbon\Carbon::now()->endOfWeek()])->get();
    //get records of current year laravel eloquent

    public function calculateInvoicesCost(array $invoices): float
    {

        $total = 0;
        $item_quantity = 0;
        $invoiceItemCounts = 0;

        $salesArray = array();
        foreach ($invoices as $invoice) {
            foreach ($invoice['invoice_items'] as $item) {

                if (isset($item['items'])) {
                    isset($salesArray[$invoice['invoice_items'][0]['items'][0]['weight_class']])
                        ? $salesArray[$invoice['invoice_items'][0]['items'][0]['weight_class']] += 1
                        : $salesArray[$invoice['invoice_items'][0]['items'][0]['weight_class']] = 1;

                    foreach ($item['items'] as $item) {
                        $total +=  ($item['unit_price'] * $item['count']);
                    }
                } else {
                    $total += $item['amount'];
                }
            }
            if (!isset($inovice['order'])) {
                continue;
            }
            try {
                $lineItems = $invoice['order']['line_items'];
                foreach ($lineItems as $lineItem) {
                    $item_quantity = $item_quantity + $lineItem['quantity'];
                }
                $delivery = $this->getOrderDeliveryCharges((object) $invoice['order']);
                $delivery = $delivery / $item_quantity;

                foreach ($invoice['invoice_items'] as $item) {
                    $invoiceItemCounts = $invoiceItemCounts + $item['count'];
                }
                $delivery = $delivery * $invoiceItemCounts;

                $total += $delivery;
            } catch (\Exception $th) {

                throw $th;
            }
        }

        $finalSales = array();
        foreach ($salesArray as $key =>  $count) {
            $finalSales[] = [
                'name'      =>      $key,
                'count'     =>      $count
            ];
        }

        return $total;
    }
    public function calculateSalesInvoicesCost(array $invoices): float
    {
        $total = 0;
        $totalCreditNote = 0;
        $item_quantity = 0;
        $invoiceItemCounts = 0;

        $salesArray = array();
        foreach ($invoices as $invoice) {

            if (isset($invoice['invoice_type']) && $invoice['invoice_type'] == 'INVOICE') {
                foreach ($invoice['invoice_items'] as $item) {

                    if (isset($invoice['invoice_type']) && $invoice['invoice_type'] == 'INVOICE') {

                        if (isset($item['items'])) {
                            isset($salesArray[$invoice['invoice_items'][0]['items'][0]['weight_class']])
                                ? $salesArray[$invoice['invoice_items'][0]['items'][0]['weight_class']] += 1
                                : $salesArray[$invoice['invoice_items'][0]['items'][0]['weight_class']] = 1;
                            foreach ($item['items'] as $item) {
                                $total +=  ($item['unit_price'] * $item['count']);
                            }
                        } else {
                            $tax = ((100 * $item['vat_rate']) + 100) / 100;

                            if ($item['taxes_included'] == 1) {
                                $total += ($item['amount'] * $item['count']) / $tax;
                            } else {
                                $total += ($item['amount'] * $item['count']);
                            }
                        }
                    }
                }
                $delivery = $this->calculateDelivery($invoice, $invoiceItemCounts, $item_quantity);
                $total += $delivery;
            } else if (isset($invoice['invoice_type']) && $invoice['invoice_type'] == 'CREDIT_NOTE') {
                foreach ($invoice['invoice_items'] as $item) {

                    $amount = $item['amount'] * $item['count'];
                    $tax = ((100 * $item['vat_rate']) + 100) / 100;

                    if ($item['taxes_included'] == 1) {
                        $totalCreditNote += ($amount / $tax);
                    } else {
                        $totalCreditNote += $amount;
                    }
                }
                $delivery = $this->calculateDelivery($invoice, $invoiceItemCounts, $item_quantity);
                $totalCreditNote += $delivery;
            }
        }

        return $total - $totalCreditNote;
    }

    public function calculateDelivery($invoice, $invoiceItemCounts, $item_quantity): float
    {
        $lineItems = $invoice['order']['line_items'];
        foreach ($lineItems as $lineItem) {
            $item_quantity = $item_quantity + $lineItem['quantity'];
        }


        if (isset($invoice['order']['shipping_lines'][0]['tax_lines'][0]['rate'])) {
            $taxRate = $invoice['order']['shipping_lines'][0]['tax_lines'][0]['rate'];
        } else {
            $taxRate = 0.00;
        }

        $isTaxIncluded = 0;
        foreach ($invoice['invoice_items'] as $item) {
            if ($item['taxes_included'] == 1) {
                $isTaxIncluded = 1;
            }
            $invoiceItemCounts = $invoiceItemCounts + $item['count'];
        }

        if ($invoice['invoice_type'] == 'CREDIT_NOTE') {
            if (!empty($invoice['shipping_lines'])) {
                $delivery = $invoice['shipping_lines']['price'] ?? 0.0;
            } else {
                $orderDelivery = $this->getOrderDeliveryCharges((object) $invoice['order']);
                $singleItemDeliveryCharges = round($orderDelivery / $item_quantity, 2);
                $delivery = $singleItemDeliveryCharges * $invoiceItemCounts;
            }
        } else {
            $orderDelivery = $this->getOrderDeliveryCharges((object) $invoice['order']);
            $singleItemDeliveryCharges = round($orderDelivery / $item_quantity, 2);
            $delivery = $singleItemDeliveryCharges * $invoiceItemCounts;
        }

        if ($isTaxIncluded && $taxRate > 0) {
            $tax = ((100 * $taxRate) + 100) / 100;
            $delivery = $delivery / $tax;
        }

        return (float) $delivery;
    }

    public function getOrderDeliveryCharges($order): float
    {
        $delivery = 0.00;

        if (isset($order?->shipping_lines[0])) {
            if ($order?->shipping_lines[0]['price_set']['shop_money']['currency_code'] == 'DKK') {
                $delivery = $order?->shipping_lines[0]['price_set']['shop_money']['amount'];
            } elseif ($order?->shipping_lines[0]['price_set']['presentment_money']['currency_code'] == 'DKK') {
                $delivery = $order?->shipping_lines[0]['price_set']['presentment_money']['amount'];
            } else {
                $delivery = $order?->shipping_lines[0]['price_set']['shop_money']['amount'];
            }
        }
        return (float) $delivery;
    }



    public function getWeightClassesStats(array $invoices, $carriers = null): array
    {
        $salesArray = array();

        foreach ($invoices as $invoice) {
            foreach ($invoice['invoice_items'] as $item) {
                if ($item['type'] == 'Shipments') {
                    if (isset($item['items'])) {

                        foreach ($item['items'] as $key => $value) {
                            $originalWeightClass = $value['weight_class'];

                            $weightClass = explode(' - ', $originalWeightClass)[1];
                            preg_match_all('/[0-9]+/', $weightClass, $matches);
                            $matches = $matches[0];
                            $string = $matches[0];

                            if (isset($matches[1])) {
                                $string .= '-' . $matches[1];
                            }
                            if (isset($matches[2])) {
                                $string .= '.' . $matches[2];
                            }
                            $weightClass = $string;

                            if ($carriers != null) {
                                foreach ($carriers as $carrier) {
                                    if (str_contains($originalWeightClass, $carrier)) {
                                        isset($salesArray[$weightClass])
                                            ? $salesArray[$weightClass] += 1
                                            : $salesArray[$weightClass] = 1;
                                    }
                                }
                            } else {
                                isset($salesArray[$weightClass])
                                    ? $salesArray[$weightClass] += 1
                                    : $salesArray[$weightClass] = 1;
                            }
                        }

                        // $originalWeightClass = $invoice['invoice_items'][0]['items'][0]['weight_class'];

                        // $weightClass = explode(' - ', $originalWeightClass)[1];

                        // preg_match_all('/[0-9]+/', $weightClass, $matches);
                        // $matches = $matches[0];

                        // $weightClass = $matches[0] . '-' . $matches[1] . '.' . $matches[2];

                        // if ($carriers != null) {
                        //     foreach ($carriers as $carrier) {
                        //         if (str_contains($originalWeightClass, $carrier)) {
                        //             isset($salesArray[$weightClass])
                        //                 ? $salesArray[$weightClass] += 1
                        //                 : $salesArray[$weightClass] = 1;
                        //         }
                        //     }
                        // } else {
                        //     isset($salesArray[$weightClass])
                        //         ? $salesArray[$weightClass] += 1
                        //         : $salesArray[$weightClass] = 1;
                        // }
                    }
                }
            }
        }

        $finalSales = array();

        foreach ($salesArray as $key =>  $count) {
            $finalSales[] = [
                'name'      =>      $key,
                'count'     =>      $count
            ];
        }

        usort($finalSales, fn($a, $b) => $a['count'] < $b['count']);

        $finalSales =  array_slice($finalSales, 0, 5);

        return $finalSales;
    }
}
