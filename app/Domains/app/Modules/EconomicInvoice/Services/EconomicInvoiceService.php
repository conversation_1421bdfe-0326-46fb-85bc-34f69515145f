<?php

declare(strict_types=1);

namespace Main\Modules\EconomicInvoice\Services;

use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;
use Core\Services\AbstractService;
use Illuminate\Support\Facades\Auth;
use Core\Exceptions\NotFoundException;
use Main\Modules\Company\Models\Company;
use Core\Exceptions\AuthenticationException;
use App\Domains\app\Services\BalanceCalculationService;
use Main\Modules\EconomicInvoice\Models\EconomicInvoice;
use Main\Modules\Transaction\Services\TransactionService;
use Main\Modules\EconomicInvoice\Response\EconomicInvoiceResponse;
use Main\Modules\EconomicInvoice\Validations\EconomicInvoiceValidation;
use App\Domains\app\Services\CreditInvoice\CreditInvoiceEconomicService;
use Main\Modules\EconomicInvoice\Repositories\EconomicInvoiceRepository;
use Main\Modules\EconomicInvoice\Authentication\EconomicInvoiceAuthentication;

class EconomicInvoiceService extends AbstractService
{
    private BalanceCalculationService $balanceService;
    private TransactionService $transactionService;
    private CreditInvoiceEconomicService $creditInvoiceEconomicService;

    public function __construct(
        EconomicInvoice $model,
        EconomicInvoiceResponse $response,
        EconomicInvoiceValidation $validator,
        EconomicInvoiceRepository $repository,
        TransactionService $transactionService,
        EconomicInvoiceAuthentication $authenticator,
        BalanceCalculationService $balanceService,
        CreditInvoiceEconomicService $creditInvoiceEconomicService,
    ) {
        $this->validator = $validator;
        $this->repository = $repository;
        $this->model = $model;
        $this->response = $response;
        $this->authenticator = $authenticator;
        $this->balanceService = $balanceService;
        $this->transactionService = $transactionService;
        $this->creditInvoiceEconomicService = $creditInvoiceEconomicService;
    }

    /**
     * @param Request $request
     *
     * @return Response
     */
    public function index(Request $request): Response
    {
        try {
            $this->authenticator->authenticate();
            $request = $request->get();
            $request['conditions']['company_code'] = Auth::user()->company_code;
            $orders = $this->repository->getOrderSearch($request, isset($request['name']['filter']) ? true : false);
            $tmp = $orders->toArray();
            $tmp['entities'] = $tmp['data'];
            unset($tmp['data']);
            $this->response->setData($tmp);
        } catch (AuthenticationException $e) {
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch (\Exception $e) {
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode(400);
        }
        return $this->response;
    }


    /**
     * @param float $amount
     * @param string $message
     * @param array $invoiceItems
     * @param int $shipmentCode
     * @param float $taxtPercent=25
     *
     * @return CreditInvoice
     */
    public function createCreditInvoice(float $amount, string $message, array $invoiceItems, int $shipmentCode, float $taxtPercent = 25): CreditInvoice
    {
        $arData = [];
        $arData['credit_note_code'] = generateHash();
        $arData['company_code'] = Auth::user()->company_code;
        $arData['shipment_code'] = $shipmentCode;
        $arData['tax_percent'] = $taxtPercent;
        $arData['description'] = $message;
        $arData['credit_note_items'] = $invoiceItems;

        return $this->repository->createCreditInvoice($arData);
    }

    /**
     * @param int $economicInvoiceCode
     *
     * @return Response
     */
    public function downloadPDF(int $economicInvoiceCode): Response
    {
        try {
            $model = $this->repository->economicInvoice($economicInvoiceCode);
            $content = view('pdf.credit-note', ['model' => $model])->render();
            $pdf = PDF::loadHTML($content)->setPaper('a4', 'portraits');
            $this->response->setData(['file' => base64_encode($pdf->output())]);
        } catch (NotFoundException $e) {
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch (\Exception $e) {
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode(400);
        }
        return $this->response;
    }

    /**
     * @param int $shipmentCode
     *
     * @return Response
     */
    public function getInvoiceFromShipmentCode(int $shipmentCode): Response
    {
        try {
            $model = $this->repository->getInvoiceFromShipmentCode($shipmentCode);

            if (!isset($model)) {
                throw new NotFoundException();
            }

            $this->response->setData($model->toArray());
            $this->response->setCode(200);
        } catch (NotFoundException $e) {
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch (\Exception $e) {
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode(400);
        }

        return $this->response;
    }

    public function generateCreditInvoices(): array
    {
        $results = [];
        $companies = Company::where('credit_enabled', true)
            ->whereNotNull('billing_cycle_start')
            ->get();

        foreach ($companies as $company) {
            if ($this->shouldGenerateInvoice($company)) {
                $result = $this->generateCreditInvoiceForCompany($company);
                $results[] = $result;
            }
        }

        return $results;
    }

    private function shouldGenerateInvoice(Company $company): bool
    {
        $nextBillingDate = Carbon::parse($company->billing_cycle_start)
            ->addDays($company->billing_cycle);

        return now()->gte($nextBillingDate);
    }

    private function generateCreditInvoiceForCompany(Company $company): array
    {
        $currentBalance = $this->balanceService->calculateBalance($company->company_code);

        if ($currentBalance >= 0) {
            info("NO Credit Used");
            return ['company_code' => $company->company_code, 'status' => 'no_credit_used'];
        }

        // getting transactions
        $transactions = $this->transactionService->getCreditInvoiceTransactions($company);

        // send transactions to economic service to generate credit invoice
        $this->creditInvoiceEconomicService->postDraftInvoice($transactions, 1234, 1234);

        // update all of the transactions, stor the credit invoice id


        // Update billing cycle start
        $company->billing_cycle_start = null;
        $company->save();

        return [
            'company_code' => $company->company_code,
            'status' => 'invoice_generated',
            'amount' => abs($currentBalance)
        ];
    }
}
