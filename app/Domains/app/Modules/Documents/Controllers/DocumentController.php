<?php

namespace Main\Modules\Documents\Controllers;

use App\Models\MonthlyInvoice;
use Barryvdh\DomPDF\Facade\Pdf;

use Core\Controllers\AbstractController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Main\Modules\Company\Models\Company;
use Main\Modules\Documents\Services\DocumentService;
use Main\Modules\Transaction\Models\Invoice;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class DocumentController extends AbstractController
{
    public function __construct(DocumentService $service)
    {
        $this->domainService = $service;
    }


    public function InvoicesDownload(Request $request, $data)
    {
        Company::with('merchant', 'country', 'merchant')->whereHas('invoice', function ($query) {
            $query->whereMonth('invoices.created_at', Carbon::now()->subMonth()->month)->where('transaction_from', 'BALANCE');
        })->where('company_code', 23000)->chunk(10, function ($companies) {

            foreach ($companies as $company) {

                $monthlyInvoice = $this->generateInvoiceRecord($company->company_code);

                $companyData = $this->getCompanyData($company, $monthlyInvoice);

                $invoices = $this->getCompanyInvoices($company->company_code);

                $countryPair = array();
                $additionalCharges = array();
                $totalAmount = 0;
                $totalAmountWithVat = 0;
                $totalVat = 0;

                $specificationCountryPair = array();
                $specificationAdditionalCharges = array();

                foreach ($invoices as $invoice) {
                    foreach ($invoice['invoice_items'] as $invoiceItems) {
                        if ($invoiceItems['type'] == 'Shipments') {
                            foreach ($invoiceItems['items'] as $item) {

                                if (isset($countryPair[$invoiceItems['country_pair']][$item['weight_class']]['counts'])) {

                                    $singleUnitPrice = $item['unit_price'] * $item['count'];

                                    $singleUnitPriceVat = $singleUnitPrice * 25 / 100;

                                    $totalVat += $singleUnitPriceVat;

                                    $singleUnitPriceWithVat = $singleUnitPrice + $singleUnitPriceVat;

                                    $totalAmountWithVat += $singleUnitPriceWithVat;

                                    $totalAmount += $singleUnitPrice;

                                    dump($singleUnitPrice . ' - ' . $singleUnitPriceVat);

                                    $counts = $countryPair[$invoiceItems['country_pair']][$item['weight_class']]['counts'];

                                    $counts['quantity']     += $item['count'];
                                    $counts['unit_price']   = $item['unit_price'];
                                    $counts['amount']       += round($item['unit_price'] * $item['count'], 2);
                                    $count['shipment_code'] =   $invoice['shipment_code'];
                                    $count['date']          =      $invoice['created_at'];

                                    $countryPair[$invoiceItems['country_pair']][$item['weight_class']]['counts'] = $counts;

                                    $counts['quantity']     = $item['count'];
                                    $specificationCountryPair[$invoiceItems['country_pair']][$item['weight_class']][$invoice['shipment_code']]['counts'] = $counts;
                                } else {

                                    $singleUnitPrice = $item['unit_price'] * $item['count'];

                                    $singleUnitPriceVat = $singleUnitPrice * 25 / 100;

                                    $singleUnitPriceWithVat = $singleUnitPrice + $singleUnitPriceVat;

                                    $totalAmount += $singleUnitPrice;

                                    $totalAmountWithVat += $singleUnitPriceWithVat;

                                    dump($singleUnitPrice . ' - ' . $singleUnitPriceVat);

                                    $counts = [
                                        'quantity'      =>  $item['count'],
                                        'unit_price'    =>  $item['unit_price'],
                                        'amount'        =>  round($item['unit_price'] * $item['count'], 2),
                                        'shipment_code' =>  $invoice['shipment_code'],
                                        'date'          =>  $invoice['created_at']
                                    ];

                                    $countryPair[$invoiceItems['country_pair']][$item['weight_class']]['counts'] = $counts;

                                    $counts['quantity']     = $item['count'];
                                    $specificationCountryPair[$invoiceItems['country_pair']][$item['weight_class']][$invoice['shipment_code']]['counts'] = $counts;
                                }
                            }
                        } elseif ($invoiceItems['type'] == 'Additional Services') {

                            foreach ($invoiceItems['items'] as $item) {

                                if (isset($additionalCharges[$item['name']]['counts'])) {

                                    $singleUnitPrice = $item['unit_price'] * $item['count'];

                                    $singleUnitPriceVat = $singleUnitPrice * 25 / 100;

                                    $singleUnitPriceWithVat = $singleUnitPrice + $singleUnitPriceVat;

                                    $totalAmountWithVat += $singleUnitPriceWithVat;

                                    dump($singleUnitPrice . ' - ' . $singleUnitPriceVat);

                                    $totalAmount += $singleUnitPrice;

                                    $counts = $additionalCharges[$item['name']]['counts'];
                                    $counts['quantity']         += $item['count'];
                                    $counts['unit_price']       = $item['unit_price'];
                                    $counts['amount']          += round($item['unit_price'] * $item['count'], 2);
                                    $counts['shipment_code']    =   $invoice['shipment_code'];
                                    $counts['date']             =   $invoice['created_at'];


                                    $additionalCharges[$item['name']]['counts'] = $counts;

                                    $counts['quantity']     = $item['count'];
                                    $specificationAdditionalCharges[$item['name']][$invoice['shipment_code']]['counts'] = $counts;
                                } else {

                                    $singleUnitPrice = $item['unit_price'] * $item['count'];

                                    $singleUnitPriceVat = $singleUnitPrice * 25 / 100;

                                    $singleUnitPriceWithVat = $singleUnitPrice + $singleUnitPriceVat;

                                    $totalAmountWithVat += $singleUnitPriceWithVat;

                                    dump($singleUnitPrice . ' - ' . $singleUnitPriceVat);

                                    $totalAmount += $singleUnitPrice;

                                    $counts = [
                                        'quantity'      =>  $item['count'],
                                        'unit_price'    =>  $item['unit_price'],
                                        'amount'        =>  round($item['unit_price'] * $item['count'], 2),
                                        'shipment_code'      =>  $invoice['shipment_code'],
                                        'date'          =>  $invoice['created_at'],
                                    ];

                                    $additionalCharges[$item['name']]['counts'] = $counts;

                                    $counts['quantity']     = $item['count'];
                                    $specificationAdditionalCharges[$item['name']][$invoice['shipment_code']]['counts'] = $counts;
                                }
                            }
                        }
                    }
                }

                $vat_amount = $totalAmount * 25 / 100;

                $vat_amount = round($vat_amount, 2);
                $totalAmount = round($totalAmount, 2);

                // final data pass to pdf view
                $finalData = [
                    'customerData' => $companyData,
                    'shipments'     =>  $countryPair,
                    'additional_charges'    =>  $additionalCharges,
                    'totalAmount'           =>  round(($totalAmountWithVat * 100) / 125, 2),
                    'vat'                   =>  '25',
                    'vat_amount'            =>  $vat_amount,
                    'total_amount_inc_vat'  =>  $totalAmountWithVat

                ];

                $finalSpecificationData = [
                    'customerData'          =>  $companyData,
                    'shipments'             =>  $specificationCountryPair,
                    'additional_charges'    =>  $specificationAdditionalCharges,
                    'totalAmount'           =>  round(($totalAmountWithVat * 100) / 125, 2),
                    'vat'                   =>  '25',
                    'vat_amount'            =>  $vat_amount,
                    'total_amount_inc_vat'  =>  $totalAmount
                ];
            }

            dd($finalData);
        });
    }

    public function generateInvoiceRecord(int $companyId): MonthlyInvoice
    {
        return MonthlyInvoice::create([
            'company_code'  =>  $companyId
        ]);
    }

    public function getCompanyData(Company $company, MonthlyInvoice $monthlyInvoice)
    {
        return [
            'name'      =>      $company->company_name,
            'address'   =>      $company->address,
            'vat'       =>      $company->vat_no,
            'date'      =>      $monthlyInvoice->created_at,                //invoice generated date
            'invoice_number'    =>  $monthlyInvoice->id,             //  invoice generated number
            'customer_number'   =>  $company->company_code,
            'month'             =>  now()->subMonth()->format('M'),
            'status'            =>  'Paid',
            'city'              => $company->city,
            'email'             => $company->merchant->email,
            'phone'             => $company->phone_no,
            'country_code'      => $company->country->iso,
            'zip'               => $company->zipcode,
        ];
    }

    public function getCompanyInvoices(int $company_id)
    {

        return Invoice::where('company_code', $company_id)
            ->where('status', 'PAID')
            ->whereMonth('created_at', '=', now()->subMonth()->month)
            ->orderBy('created_at', 'DESC')
            ->get();
    }
}
