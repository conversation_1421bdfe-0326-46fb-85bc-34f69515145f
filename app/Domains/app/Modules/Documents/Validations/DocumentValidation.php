<?php

namespace Main\Modules\Documents\Validations;

use Illuminate\Validation\Rule;

use Core\Validation\AbstractValidation;
use Core\Contracts\Validation\Validation;

class DocumentValidation extends AbstractValidation
{
    public function store(): Validation
    {
        $this->rules = [];

        return $this;
    }

    public function update(): Validation
    {
        $this->rules = [];
        
        return $this;
    }
}