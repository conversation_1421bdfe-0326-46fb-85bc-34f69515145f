<?php

declare(strict_types=1);

namespace Main\Modules\Documents\Services;

use Barryvdh\DomPDF\Facade\Pdf;
use Core\Contracts\Response\Response;
use Core\Services\AbstractService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Main\Modules\Documents\Response\DocumentResponse;
use Main\Modules\Documents\Validations\DocumentValidation;
use Main\Modules\Order\Models\Order;
use Picqer\Barcode\BarcodeGeneratorPNG;

/**
 * @property DocumentValidation $validator
 * @property DocumentResponse   $response
 */
class DocumentService extends AbstractService
{
    public function __construct(
        DocumentValidation $validator,
        DocumentResponse $response
    ) {
        $this->validator = $validator;
        $this->response = $response;
    }

    public function generatePickList(Request $request): Response
    {
        $queryParams = $request->query();

        if (!isset($queryParams['order_codes']) || (count(explode(',', $queryParams['order_codes'])) == 0)) {
            $this->response->setData(['error' => 'Order code(s) missing in the parameter']);
            $this->response->setCode(400);
            return $this->response;
        }

        $orderCodes = explode(',', $queryParams['order_codes']);
        $orderCodes = array_map(
            function ($item) {
                return trim($item);
            },
            $orderCodes
        );

        $fileContent = [];
        $generator = new BarcodeGeneratorPNG();

        $orders = Order::with(['lineItems', 'lineItems.product', 'company', 'deliveryMethod'])
            ->whereIn('order_code', $orderCodes)
            ->get();

        try {
            $getFooterTemplateDeatil  = DB::table('sa_template')->where('type', 'FOOTER')->first();
            $footer = ($getFooterTemplateDeatil->summary) ?? '';
            foreach ($orders as $order) {
                $barCode = $generator->getBarCode((string)$order->order_code, $generator::TYPE_CODE_128);
                $fileContent[] = view(
                    'pdf.pick_list',
                    [
                        'order' => $order,
                        'barCode' => $barCode,
                        'company' => $order->company,
                        'createdAt' => now(),
                        'footer' => $footer,

                    ]
                )->render();
            }



            $content = view('pdf.main', ['content' => implode('<div class="page-break"></div>', $fileContent)])->render();
            $pdf = PDF::loadHTML($content)->setPaper('a4', 'portrait');
            $pdf->output();
            $dom_pdf = $pdf->getDomPDF();
            $canvas = $dom_pdf->get_canvas();
            $canvas->page_text(515, 795, "Page {PAGE_NUM} of {PAGE_COUNT}", null, 8, array(0.3, 0.3, 0.3));
            $this->response->setData(['file' => base64_encode($pdf->output())]);
        } catch (\Exception $e) {
            error_log($e->getTraceAsString());
            $this->response->setData(['error' => $e->getMessage()]);
        }

        return $this->response;
    }

    public function generatePackingSlip(Request $request): Response
    {
        $queryParams = $request->query();

        if (!isset($queryParams['order_codes']) || (count(explode(',', $queryParams['order_codes'])) == 0)) {
            $this->response->setData(['error' => 'Order code(s) missing in the parameter']);
            $this->response->setCode(400);
            return $this->response;
        }

        $orderCodes = explode(',', $queryParams['order_codes']);
        $orderCodes = array_map(
            function ($item) {
                return trim($item);
            },
            $orderCodes
        );

        $fileContent = [];

        $orders = Order::with(['lineItems', 'lineItems.product', 'company'])
            ->whereIn('order_code', $orderCodes)
            ->get();

        try {
            $getFooterTemplateDeatil  = DB::table('sa_template')->where('type', 'FOOTER')->first();
            $footer = ($getFooterTemplateDeatil->summary) ?? '';
            foreach ($orders as $order) {
                $fileContent[] = view(
                    'pdf.packing_slip',
                    [
                        'order' => $order,
                        'company' => $order->company,
                        'createdAt' => now(),
                        'footer' => $footer
                    ]
                )->render();
            }

            $content = view('pdf.main', ['content' => implode('<div class="page-break"></div>', $fileContent)])->render();
            $pdf = PDF::loadHTML($content)->setPaper('a4', 'portrait');
            $pdf->output();
            $dom_pdf = $pdf->getDomPDF();
            $canvas = $dom_pdf->get_canvas();
            $canvas->page_text(515, 795, "Page {PAGE_NUM} of {PAGE_COUNT}", null, 8, array(0.3, 0.3, 0.3));

            $this->response->setData(['file' => base64_encode($pdf->output())]);
        } catch (\Exception $e) {
            error_log($e->getTraceAsString());
            $this->response->setData(['error' => $e->getMessage()]);
        }

        return $this->response;
    }
}
