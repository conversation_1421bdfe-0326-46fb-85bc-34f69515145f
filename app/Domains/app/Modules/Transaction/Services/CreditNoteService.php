<?php
declare(strict_types=1);

namespace Main\Modules\Transaction\Services;

use Barryvdh\DomPDF\Facade\Pdf;

use Illuminate\Support\Facades\Auth;
// use Illuminate\Support\Str;

use Core\Services\AbstractService;
use Core\Contracts\Request\Request;
use Core\Contracts\Response\Response;
use Core\Exceptions\AuthenticationException;
use Core\Exceptions\NotFoundException;
use Core\Exceptions\ValidationException;

use Main\Modules\Transaction\Authentication\TransactionAuthentication;
use Main\Modules\Transaction\Validations\CreditNoteValidation;
use Main\Modules\Transaction\Repositories\CreditNoteRepository;
use Main\Modules\Transaction\Response\CreditNoteResponse;
use Main\Modules\Transaction\Models\CreditNote;

class CreditNoteService extends AbstractService
{
    public function __construct(
        CreditNoteValidation $validator,
        CreditNoteRepository $repository,
        CreditNoteResponse $response,
        CreditNote $model,
        TransactionAuthentication $authenticator
    ) {
        $this->validator = $validator;
        $this->repository = $repository;
        $this->model = $model;
        $this->response = $response;
        $this->authenticator = $authenticator;
    }

    /**
     * @param Request $request
     *
     * @return Response
     */
    public function index(Request $request): Response
    {
        try {
            $this->authenticator->authenticate();

            $request = $request->get();
            $request['conditions']['company_code'] = Auth::user()->company_code;
            $data = $this->repository->index($request);
            $this->response->setData($data->toArray());
        } catch(AuthenticationException $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch(\Exception $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode($e->getCode());
        }
        return $this->response;
    }


    /**
     * @param float $amount
     * @param string $message
     * @param array $invoiceItems
     * @param int $shipmentCode
     * @param float $taxtPercent=25
     *
     * @return CreditNote
     */
    public function createCreditNote(float $amount, string $message, array $invoiceItems, int $shipmentCode, float $taxtPercent=25): CreditNote
    {
        $arData = [];
        $arData['credit_note_code'] = generateHash();
        $arData['company_code'] = Auth::user()->company_code;
        $arData['shipment_code'] = $shipmentCode;
        $arData['tax_percent'] = $taxtPercent;
        $arData['description'] = $message;
        $arData['credit_note_items'] = $invoiceItems;

        return $this->repository->createCreditNote($arData);
    }

    /**
     * @param int $creditNoteCode
     *
     * @return Response
     */
    public function downloadPDF(int $creditNoteCode): Response
    {
        try {
            $model = $this->repository->creditNote($creditNoteCode);

            $content = view('pdf.credit-note', ['model'=>$model])->render();

            $pdf = PDF::loadHTML($content)->setPaper('a4', 'portraits');
            // $pdf->save(storage_path().'/app/test-credit-note.pdf');

            $this->response->setData(['file'=>base64_encode($pdf->output())]);

        } catch(NotFoundException $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch(\Exception $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode(400);
        }
        return $this->response;
    }

    /**
     * @param int $shipmentCode
     *
     * @return Response
     */
    public function getInvoiceFromShipmentCode(int $shipmentCode): Response
    {
        try {
            $model = $this->repository->getInvoiceFromShipmentCode($shipmentCode);

            if (!isset($model)) {
                throw new NotFoundException();
            }

            $this->response->setData($model->toArray());
            $this->response->setCode(200);
        } catch(NotFoundException $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch(\Exception $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode(400);
        }

        return $this->response;
    }
}
