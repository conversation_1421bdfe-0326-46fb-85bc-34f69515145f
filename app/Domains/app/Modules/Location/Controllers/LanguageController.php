<?php
namespace Main\Modules\Location\Controllers;

use Core\Controllers\AbstractController;

use Main\Modules\Location\Requests\LanguageRequest;
use Main\Modules\Location\Services\LanguageService;

class LanguageController extends AbstractController
{
    /**
     * @param LanguageService $service
     * @param LanguageRequest $request
     */
    public function __construct(LanguageService $service, LanguageRequest $request)
    {
        $this->domainService = $service;
        $this->domainRequest = $request;
    }
}