<?php

namespace Main\Modules\Location\Controllers;

use App\Models\MerchantAgreement;
use Core\Controllers\AbstractController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Main\Modules\Location\Models\Country;
use Main\Modules\Location\Requests\CountryRequest;
use Main\Modules\Location\Services\CountryService;
use Main\Modules\Shipment\Authentication\ShipmentsAuthentication;

class CountryController extends AbstractController
{
    private $authenticator;
    /**
     * @param CountryService $service
     * @param CountryRequest $request
     */
    public function __construct(CountryService $service, CountryRequest $request, ShipmentsAuthentication $authenticator,)
    {
        $this->domainService = $service;
        $this->domainRequest = $request;
        $this->authenticator = $authenticator;
    }

    public function getMerchantAgreementCountries()
    {
        $this->authenticator->authenticate();

        $user_id = Auth::user()->user_code;

        $user_agreement = MerchantAgreement::where('merchant_id', $user_id)->with('pricing.priceGroup', 'pricing.ShipvagooPricing.ubsend_price')->first();

        if ($user_agreement) {
            foreach ($user_agreement->pricing as $pricing) {

                $countries[] = $pricing->ShipvagooPricing->ubsend_price->to_country;
            }

            $countries = array_unique($countries);

            $finalCountries = [];

            foreach ($countries as $countries) {
                $finalCountries[] = Country::where('default_name', $countries)->first();
            }

            return $finalCountries;
        }

        return [];
    }
}
