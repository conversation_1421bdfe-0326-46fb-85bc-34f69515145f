<?php

namespace Main\Modules\Location\Validations;

use Core\Validation\AbstractValidation;
use Core\Contracts\Validation\Validation;

class LanguageValidation extends AbstractValidation
{
    public function store(): Validation
    {
        $this->rules = [];

        return $this;
    }

    public function update(): Validation
    {
        $this->rules = [];
        
        return $this;
    }
}