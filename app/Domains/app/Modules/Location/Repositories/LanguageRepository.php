<?php

namespace Main\Modules\Location\Repositories;

use Core\Repositories\AbstractRepository;

use Main\Modules\Location\Factories\LanguageFactory;
use Main\Modules\Location\Collections\LanguageCollection;
use Main\Modules\Location\Models\Language;

class LanguageRepository extends AbstractRepository
{
    /**
     * @param LanguageFactory    $factory
     * @param LanguageCollection $collection
     * @param Language           $model
     */
    public function __construct(LanguageFactory $factory, LanguageCollection $collection, Language $model)
    {
        $this->factory = $factory;
        $this->collection = $collection;
        $this->model = $model;
    }
}