<?php

namespace Main\Modules\Location\Repositories;

use Core\Repositories\AbstractRepository;

use Main\Modules\Location\Factories\CountryFactory;
use Main\Modules\Location\Collections\CountryCollection;
use Main\Modules\Location\Models\Country;

class CountryRepository extends AbstractRepository
{
    /**
     * @param CountryFactory    $factory
     * @param CountryCollection $collection
     * @param Country           $model
     */
    public function __construct(CountryFactory $factory, CountryCollection $collection, Country $model)
    {
        $this->factory = $factory;
        $this->collection = $collection;
        $this->model = $model;
    }

    public function country(string $country): Country
    {
        return $this->model->where('name', '=', $country)->first();
    }

    public function getCountryByIso(string $iso): Country
    {
        return $this->model->where('iso', '=', $iso)->first();
    }

    public function getCountryByCode(int $countryCode): Country
    {
        return $this->model->where('country_code', '=', $countryCode)->first();
    }
    public function getCountryByReceiverCode(int $receiver_code):Country
    {
        return $this->model->where('country_code', '=', $receiver_code)->first();

    }
}
