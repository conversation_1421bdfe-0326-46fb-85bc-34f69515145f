<?php
declare(strict_types=1);

namespace Main\Modules\Location\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;

use Core\Models\AbstractModel;

class Language extends AbstractModel
{
    protected $primaryKey = 'language_code';
    public $incrementing = false;
    protected $fillable = ['language_code', 'name'];
    protected array $relationships = [];

    protected function languageCode(): Attribute
    {
        return Attribute::make(
            set: fn() => generateHash()
        );
    }
}