<?php
declare(strict_types=1);

namespace Main\Modules\Location\Services;

use Core\Services\AbstractService;
use Core\Contracts\Request\Request;
use Core\Contracts\Response\Response;

use Main\Modules\Location\Validations\LanguageValidation;
use Main\Modules\Location\Repositories\LanguageRepository;
use Main\Modules\Location\Response\LanguageResponse;
use Main\Modules\Location\Models\Language;

class LanguageService extends AbstractService
{
    /**
     * @param LanguageValidation $validator
     * @param LanguageRepository $repository
     * @param LanguageResponse   $response
     * @param Language           $model
     */
    public function __construct(LanguageValidation $validator, LanguageRepository $repository, LanguageResponse $response, Language $model)
    {
        $this->validator = $validator;
        $this->repository = $repository;
        $this->model = $model;
        $this->response = $response;
    }

    /**
     * @param Request $request
     * 
     * @return Response
     */
    public function index(Request $request): Response
    {
        $request->add('paginate', false);
        return parent::index($request);
    }
}