<?php
declare(strict_types=1);

namespace Main\Modules\Location\Services;

use Core\Services\AbstractService;
use Core\Contracts\Request\Request;
use Core\Contracts\Response\Response;

use Main\Modules\Location\Validations\CountryValidation;
use Main\Modules\Location\Repositories\CountryRepository;
use Main\Modules\Location\Response\CountryResponse;
use Main\Modules\Location\Models\Country;

class CountryService extends AbstractService
{
    /**
     * @param CountryValidation $validator
     * @param CountryRepository $repository
     * @param CountryResponse   $response
     * @param Country           $model
     */
    public function __construct(CountryValidation $validator, CountryRepository $repository, CountryResponse $response, Country $model)
    {
        $this->validator = $validator;
        $this->repository = $repository;
        $this->model = $model;
        $this->response = $response;
    }

    /**
     * @param Request $request
     *
     * @return Response
     */
    public function index(Request $request): Response
    {
        $request->add('paginate', false);
        return parent::index($request);
    }

    public function getCountryByIso(string $iso)
    {
        return $this->repository->getCountryByIso($iso);
    }

    public function getCountryByCode(int $countryCode)
    {
        return $this->repository->getCountryByCode($countryCode);
    }
}
