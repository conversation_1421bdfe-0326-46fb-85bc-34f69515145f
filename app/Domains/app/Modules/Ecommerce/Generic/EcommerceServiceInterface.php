<?php

namespace Main\Modules\Ecommerce\Generic;

interface EcommerceServiceInterface
{
    public function getCustomer($shopUrl, $orderId, $accessToken);

    public function updateOrderShippingAddress($shopUrl, $accessToken, $orderId, $shippingAddress, $email);

    public function getSingleProduct($shopUrl, $accessToken, $productId);

    public function calculateRefund($shopUrl, $accessToken, $orderID, $post_data);

    public function capturePayment($shopUrl, $accessToken, $orderID, $post_data);

    public function getRemainingCapturableBalance($shopUrl, $accessToken, $orderID);

    public function markAsPaid($shopUrl, $accessToken, $orderID, $post_data);

    public function createRefund($shopUrl, $accessToken, $orderID, $post_data);

    public function create_webhook($shop, $type, $callbacK_url, $format);

    public function createFulfillment($shopUrl, $accessToken, $orderID, $post_data);

    public function getFulfillment($shopUrl, $accessToken, $fulfillmentId);

    public function cancelFulfillment($shopUrl, $accessToken, $fulfillmentId);

    public function getOrderFulfillment($shopUrl, $accessToken, $orderID);

    public function getOrderDetails($shopUrl, $accessToken, $orderID);

    public function getOrderTransactions($shopUrl, $accessToken, $orderID);
}
