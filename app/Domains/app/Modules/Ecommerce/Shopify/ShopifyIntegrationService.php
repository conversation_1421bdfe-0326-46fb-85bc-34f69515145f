<?php

namespace Main\Modules\Ecommerce\Shopify;

use Carbon\Carbon;
use App\Models\WebhookTopic;
use Illuminate\Http\Request;
use App\Jobs\CreateWebhookJob;
use App\Utils\WebhookTopicEnum;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Main\Modules\Location\Models\Country;
use Main\Modules\Setting\Models\Currency;
use Main\Modules\Platforms\Models\Platform;
use Main\Modules\Platforms\Services\IntegrationService;
use Main\Modules\Ecommerce\Generic\EcommerceIntegrationServiceInterface;

class ShopifyIntegrationService implements EcommerceIntegrationServiceInterface
{

    public function __construct(private ShopifyRepository $repository, private IntegrationService $integrationService) {}

    public function install_ecommerce_app_callback(Request $request)
    {
        try {
            $company_code = $request->state;
            $shopUrl = $request->shop;
            $code = $request->code;

            if (empty($shopUrl) || empty($code)) {
                return Redirect::to(config('custom.frontend_app_url') . "/order-integration?error_message=Required%20callback%20data%20missing");
            }

            $accessToken = $this->repository->getToken($shopUrl, $code);
            if (!$accessToken || empty($accessToken))
                return Redirect::to(config('custom.frontend_app_url') . "/order-integration?error_message=Unable%20to%20retrieve%20shop%20access%20token.Please%20restart%20the%20app%20from%20apps%20listing.");

            $shop_data = $this->repository->getShopData($shopUrl, $accessToken);

            if (!$shop_data || empty($shop_data)) {
                return Redirect::to(config('custom.frontend_app_url') . "/order-integration?error_message=unable%20to%20retrieve%20shop%20data");
            } else {
                $shop = $this->integrationService->getIntegrationByString('api_url', $shopUrl);

                if ($shop && $shop->company_code != $company_code) {
                    return Redirect::to(config('custom.frontend_app_url') . "/order-integration?error_message=This%20Store%20already%20integrated%20by%20some%20other%20user%21");
                }

                if (!$shop) {
                    $shop = [
                        'integration_code' => $shop->integration_code ?? Carbon::now()->timestamp,
                        'company_code' => $company_code,
                        'platform_code' => Platform::where('name', 'Shopify')->first()->platform_code,
                        'api_secret' => $accessToken,
                        'api_username' => $accessToken,
                        'accesstoken' => $accessToken,

                        /** modify according to gl response */
                        "name" => ucwords(str_replace('_', ' ', str_replace('-', ' ', $shop_data['name']))),
                        "email" => $shop_data['email'],
                        "api_url" => $shop_data['primary_domain']['url'] ?? null,
                        "address" => $shop_data['billing_address']['address1'] ?? null,
                        "address_2" => $shop_data['billing_address']['address2'] ?? "",
                        "zipcode" => $shop_data['billing_address']['zip'] ?? null,
                        "city" => $shop_data['billing_address']['city'] ?? null,
                        "phone_no" => $shop_data['billing_address']['phone'] ?? null,
                        "currency" => $shop_data['currency_code'] ?? null,
                        "shop_id" => $shop_data['id'] ?? null,
                        "myshopify_domain" => $shop_data['myshopify_domain'] ?? null,
                        "country_code" => Country::where("iso", $shop_data['billing_address']['country_code_v2'])->value('country_code') ?? null,
                        "currency_code" => Currency::where("short_form", $shop_data['currency_code'])->value('currency_code') ?? null,
                        "status" => 1,
                    ];

                    $this->integrationService->createIntegration($shop);
                    $shopData = array();
                    $shopData['api_url'] = $shop_data['myshopify_domain'];
                    $shopData['accesstoken'] = $accessToken;
                    $this->verifyWebhooks($shopData);
                    $this->createShippingCarrier($shopData);

                    return Redirect::to(config('custom.frontend_app_url') . "/order-integration?integration_code=" . $shop['integration_code']);
                }
            }

            return Redirect::to(config('custom.frontend_app_url') . "/order-integration");
        } catch (\Exception $e) {
            return Redirect::to(config('custom.frontend_app_url') . "/order-integration?error_message=Sorry!%20something%20went%20wrong%20.");
        }
    }

    function verifyWebhooks($shop)
    {
        try {
            $shopUrl = $shop['api_url'];
            $accessToken = $shop['accesstoken'];

            $webhooks = $this->repository->fetchWebhooks($shopUrl, $accessToken);
            $required_webhook_topics = WebhookTopic::where('topic_status', '=', 'enabled')->get();

            if ($webhooks && sizeof($webhooks) > 0) {
                $str = '';
                foreach ($required_webhook_topics as $webhook_topic):
                    // $address = URL::to('/') . '/' . $webhook_topic->webhook_topic_url;
                    $webhook_topic_already_exists = false;
                    foreach ($webhooks as $webhook):
                        $str .= $webhook['topic'] . "\n";
                        if ($webhook['topic'] == $webhook_topic->topic_name) {
                            $webhook_topic_already_exists = true;
                            break;
                        }

                    endforeach;
                    if (!$webhook_topic_already_exists) {
                        $this->create_webhook($shop, $webhook_topic->topic_name, $webhook_topic->webhook_topic_url);
                    }
                endforeach;
            } else {
                foreach ($required_webhook_topics as $key => $webhook_topic):
                    $topic = WebhookTopicEnum::convertRestFormatToTopicEnum($webhook_topic->topic_name);
                    // job for creating webhookes with a delay of +1 second for each.
                    CreateWebhookJob::dispatch($shop, $topic, $webhook_topic->webhook_topic_url, 'shopify', 'JSON')
                        ->onQueue('products-webhook')
                        ->delay(now()->addSeconds($key + 1));

                endforeach;
                $this->create_webhook($shop, 'app/uninstalled', 'appunistalled_webhook');
            }
        } catch (\Exception $exception) {
            info("Exception " . $exception->getMessage());
            return false;
        }
    }

    public function create_webhook($shop, $type, $callbacK_url)
    {
        $shopUrl = $shop['api_url'];
        $accessToken = $shop['accesstoken'];
        $callbacK_url = env('WEBHOOK_CALLBACK_URL') . $callbacK_url;
        $post_data =  array('topic' => WebhookTopicEnum::convertRestFormatToTopicEnum($type), 'address' => $callbacK_url, 'format' => 'JSON');
        try {
            $this->repository->createWebhook($shopUrl, $accessToken, $post_data);
            Log::info("webhook created for $type");
            return true;
        } catch (\Exception $e) {
            Log::info("webhook failed for " . $e->getMessage());
            return false;
        }
    }

    public function createShippingCarrier($shop)
    {
        try {
            $shopUrl = $shop['api_url'];
            $accessToken = $shop['accesstoken'];
            try {
                $this->repository->createShippingCarrier($shopUrl, $accessToken);
                return true;
            } catch (\Exception $e) {
                return false;
            }
        } catch (\Exception $exception) {
            throw new \Exception($exception->getMessage());
        }
    }

    public function open_ecommerce_app(Request $request)
    {
        $shopUrl = $request->shop;
        if ($request->shop) {
            $shop = $this->integrationService->getIntegrationByString('api_url', $shopUrl);
            if (!$shop) return Redirect::to(config('custom.frontend_app_url') . "/login?error_message=Could%20not%20find%20shop%20data.%20Install%20app%20from%20order%20integrations%20or%20contact%20with%20shipvagoo%20team%20for%20an%20account");

            $scope = config('shops.scope');
            $scope = explode(',', $scope);
            $redirectUrl = env('WEBHOOK_CALLBACK_URL') . '/shopify/install_ecommerce_app_callback';

            $shopify = $this->repository->setShopUrl($shopUrl);
            $state = 23000;
            $url = $shopify->getAuthorizeUrl($scope, $redirectUrl, $state);
            return Redirect::to($url);
        } else {
            return Redirect::to(config('custom.frontend_app_url') . "/login?error_message=Required%20shop%20data%20missing");
        }
    }
}
