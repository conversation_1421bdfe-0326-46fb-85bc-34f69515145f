<?php

namespace Main\Modules\Ecommerce\Shopify;

use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Jobs\OrdersProcessJob;
use App\Jobs\ProductsProcessJob;
use App\Jobs\CustomersProcessJob;
use App\Jobs\OrderEditProcessJob;
use App\Jobs\OrderRefundProcessJob;
use App\Jobs\ShopProfileProcessJob;
use App\Models\IncomingShopifyWebhook;
use App\Jobs\ShopifyFulfillmentSyncJob;
use App\Jobs\ShopifyFulfillmentHoldAndReleaseJob;
use Main\Modules\Ecommerce\Generic\EcommerceWebhookReceiverInterface;

class ShopifyWebhookService implements EcommerceWebhookReceiverInterface
{
    public function receiveWebhook(Request $request): void
    {
        $event_topic = $request->header('x-shopify-topic');
        $store_domain = $request->header('x-shopify-shop-domain');
        $check_not_duplicates = $this->handleDuplicateWebhooks($store_domain, $event_topic, $request->all());

        if ($check_not_duplicates) {
            $this->dispatchWebhook($event_topic, $store_domain, $request->all(), $request->id);
        }
    }

    public function handleDuplicateWebhooks($store_domain, $event_topic, $data)
    {
        if ($event_topic == 'orders/edited') {
            $data = $data['order_edit'];
        }

        if (Str::contains($event_topic, 'locales')) {
            return true;
        }

        $object_id = $data['id'] ?? $data['inventory_item_id'] ?? 0;
        $shopify_updated_at = $data['updated_at'] ?? now();
        try {
            $log = new IncomingShopifyWebhook();
            $log->object_id = $object_id;
            $log->shop_name = $store_domain;
            $log->topic_name = $event_topic;
            $log->shopify_updated_at = $shopify_updated_at;
            $log->save();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }


    function dispatchWebhook($event_topic, $store_domain, $shopify_request_data, $webhook_id)
    {
        $integration = \Main\Modules\Platforms\Models\Integration::where('api_url', 'LIKE', "%$store_domain%")->whereNull('deleted_at')->with('platform')->first();
        if ($integration) {
            if ($integration->platform !== null) {
                $integrationData = array_merge($integration->toArray(), ['platform_name' => $integration->platform->name]);
                if ($event_topic == 'customers/create' || $event_topic == 'customers/update') {
                    CustomersProcessJob::dispatch($integrationData['integration_code'], $integrationData['company_code'], $shopify_request_data, $webhook_id)
                        ->onQueue('customers-webhook');
                } elseif ($event_topic == 'products/create' || $event_topic == 'products/update') {
                    ProductsProcessJob::dispatch($integrationData['integration_code'], $integrationData['company_code'], $shopify_request_data, $webhook_id)
                        ->onQueue('products-webhook');
                } elseif ($event_topic == 'orders/create' || $event_topic == 'orders/updated') {
                    OrdersProcessJob::dispatch($integrationData['platform_code'], $integrationData['integration_code'], $integrationData['company_code'], $shopify_request_data, $webhook_id)
                        ->onQueue('orders-webhook');
                } elseif ($event_topic == 'orders/edited') {
                    OrderEditProcessJob::dispatch($integrationData['platform_code'], $integrationData['integration_code'], $integrationData['company_code'], $shopify_request_data, $webhook_id)
                        ->onQueue('orders-webhook');
                } elseif ($event_topic == 'refunds/create') {
                    OrderRefundProcessJob::dispatch($shopify_request_data, $webhook_id)
                        ->onQueue('orders-webhook');
                } elseif ($event_topic == 'shop/update') {
                    ShopProfileProcessJob::dispatch($shopify_request_data, $webhook_id)
                        ->onQueue('customers-webhook');
                } elseif ($event_topic == 'fulfillments/create' || $event_topic == 'fulfillments/update') {
                    ShopifyFulfillmentSyncJob::dispatch($integrationData['platform_code'], $integrationData['integration_code'], $integrationData['company_code'], $shopify_request_data, $webhook_id)
                        ->onQueue('orders-webhook');
                } elseif ($event_topic == 'fulfillment_orders/placed_on_hold' || $event_topic == 'fulfillment_orders/hold_released') {
                    ShopifyFulfillmentHoldAndReleaseJob::dispatch($integrationData['platform_code'], $integrationData['integration_code'], $integrationData['company_code'], $shopify_request_data, $webhook_id, $event_topic)
                        ->onQueue('orders-webhook');
                }
            }
        }
    }
}
