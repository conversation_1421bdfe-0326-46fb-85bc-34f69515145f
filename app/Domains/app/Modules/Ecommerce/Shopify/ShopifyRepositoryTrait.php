<?php

namespace Main\Modules\Ecommerce\Shopify;

use App\Utils\ShopifyIntegrationManager;

trait ShopifyRepositoryTrait
{
    private $accessToken;
    private $shopUrl;
    private  $glClient;

    public function initiliaze(string $shopUrl, string $accessToken)
    {
        $this->accessToken = $accessToken;
        $this->shopUrl = $shopUrl;
        $this->glClient = ShopifyIntegrationManager::getInstance($this->accessToken, $this->shopUrl)
            ->getGraphqlClient();

        if (!$this->glClient) {
            throw new \Exception("Graphql client initialization failed.");
        }
        return $this;
    }
    /*
     * Set Shop  Url;
     */
    public function setShopUrl($shopUrl)
    {
        $url = parse_url($shopUrl);
        $this->shopDomain = isset($url['host']) ? $url['host'] : $this->removeProtocol($shopUrl);
        return $this;
    }

    public function removeProtocol($url)
    {
        $disallowed = ['http://', 'https://', 'http//', 'ftp://', 'ftps://'];
        foreach ($disallowed as $d) {
            if (strpos($url, $d) === 0) {
                return str_replace($d, '', $url);
            }
        }

        return $url;
    }

    public function getAccessToken($code)
    {
        $uri = "admin/oauth/access_token";
        $payload = ["client_id" => config('shops.clientId'), 'client_secret' => config('shops.clientSecret'), 'code' => $code];
        $response = $this->makeRequest('POST', $uri, $payload);

        return $response ?? '';
    }

    private function makeRequest($method, $uri, $params = [], $headers = [])
    {
        $query = in_array($method, ['get', 'delete']) ? "query" : "json";
        $rateLimit = explode("/", $this->getHeader("X-Shopify-Shop-Api-Call-Limit"));

        if ($rateLimit[0] >= 38) sleep(15);
        $response = $this->client->request(strtoupper($method), $this->baseUrl() . $uri, [
            'headers' => array_merge($headers, $this->requestHeaders),
            $query => $params,
            'timeout' => 120.0,
            'connect_timeout' => 120.0,
            'http_errors' => false,
            "verify" => false
        ]);

        $this->parseResponse($response);
        $responseBody = $this->responseBody($response);

        return (is_array($responseBody) && (count($responseBody) > 0)) ? array_shift($responseBody) : $responseBody;
    }

    private function parseResponse($response)
    {
        $this->parseHeaders($response->getHeaders());
    }

    private function parseHeaders($headers)
    {
        foreach ($headers as $name => $values) {
            $this->responseHeaders = array_merge($this->responseHeaders, [$name => implode(', ', $values)]);
        }
    }

    private function responseBody($response)
    {
        return json_decode($response->getBody(), true);
    }


    public function getHeader($header)
    {
        return $this->hasHeader($header) ? $this->responseHeaders[$header] : '';
    }

    public function hasHeader($header)
    {
        return array_key_exists($header, $this->responseHeaders);
    }

    private function baseUrl()
    {
        return "https://{$this->shopDomain}/";
    }

    public function setAccessToken($accessToken)
    {
        $this->accessToken = $accessToken;

        return $this;
    }

    private function setXShopifyAccessToken()
    {
        return ['X-Shopify-Access-Token' => $this->accessToken];
    }

    private function convertResponseToCollection($response)
    {
        return collect(json_decode(json_encode($response)));
    }

    /**
     *  Load fragments from the file system
     * @param array $fragments
     * @return string
     */
    private function loadFragments(array $fragments): string
    {
        return collect($fragments)
            ->map(fn($fragment) => file_get_contents(resource_path("graphql/fragments/{$fragment}.graphql")))
            ->implode("\n");
    }

    public function getAuthorizeUrl($scope = [] || '', $redirect_url = '', $nonce = '')
    {
        if (is_array($scope)) $scope = implode(",", $scope);

        $url = "https://{$this->shopDomain}/admin/oauth/authorize?client_id=" . config('shops.clientId') . "&scope=" . urlencode($scope);

        if ($redirect_url != '') $url .= "&redirect_uri=" . urlencode($redirect_url);

        if ($nonce != '') $url .= "&state=" . urlencode($nonce);

        return $url;
    }
}
