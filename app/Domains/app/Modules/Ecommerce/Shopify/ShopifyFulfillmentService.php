<?php

namespace Main\Modules\Ecommerce\Shopify;

use Main\Events\SaveOrderEvent;
use App\Services\ShopifyService;
use Core\Services\AbstractService;
use Illuminate\Support\Facades\DB;
use Core\Contracts\Request\Request;
use Illuminate\Support\Facades\Auth;
use Core\Contracts\Response\Response;
use Main\Modules\Order\Entities\Order;
use Main\Modules\Order\Models\LineItem;
use Core\Exceptions\ValidationException;
use Main\Modules\Company\Models\Company;
use Main\Modules\Location\Models\Country;
use Main\Modules\Order\Models\Fulfillment;
use Main\Modules\Order\Models\OrderRefund;
use Main\Modules\Shipment\Models\ASCharge;
use Core\Exceptions\AuthenticationException;
use Main\Events\CreateOrderFulfillmentEvent;
use Main\Modules\Order\Entities\SalesInvoice;
use Main\Modules\Order\Requests\OrderRequest;
use Main\Modules\Order\Services\OrderService;
use Main\Modules\Order\Models\Order as OrderModel;
use Main\Modules\Shipment\Services\ShipmentService;
use Main\Modules\Order\Repositories\OrderRepository;
use Main\Modules\Order\Response\FulfillmentResponse;
use Main\Modules\Location\Repositories\CountryRepository;
use Main\Modules\Order\Validations\FulfillmentValidation;
use Main\Modules\Order\Authentication\OrderAuthentication;
use Main\Modules\Order\Repositories\FulfillmentRepository;
use Main\Modules\Ecommerce\Generic\EcommerceServiceInterface;
use Main\Modules\Ecommerce\Generic\EcommerceFulfillmentServiceInterface;

class ShopifyFulfillmentService extends AbstractService implements EcommerceFulfillmentServiceInterface
{

    public OrderService $orderService;
    public $fulfillmentRequest;
    public ShopifyService $shopifyService;
    public OrderRepository $orderRepository;
    public CountryRepository $countryRepository;
    public $domainRequest;
    public $orderWithDynamicRelations = [
        'fulfillments',
        'fulfillmentReturns',
        'cancelledFulfillments',
        'deliveryTemplate',
        'customer'
    ];

    public function __construct(
        FulfillmentValidation $validator,
        FulfillmentRepository $repository,
        FulfillmentResponse   $response,
        Fulfillment           $model,
        OrderAuthentication   $authenticator,
        OrderService          $orderService,
        OrderRequest          $request,
        ShopifyService $shopifyService,
        OrderRepository $orderRepository,
        CountryRepository $countryRepository,
    ) {
        $this->validator = $validator;
        $this->repository = $repository;
        $this->orderRepository = $orderRepository;
        $this->countryRepository = $countryRepository;
        $this->model = $model;
        $this->response = $response;
        $this->authenticator = $authenticator;
        $this->orderService = $orderService;
        $this->domainRequest = $request;
        $this->shopifyService = $shopifyService;
    }

    public function setShipmentCode(int $orderCode, int $fulfillmentCode, int $shipmentCode): void
    {
        $this->repository->setShipmentCode($orderCode, $fulfillmentCode, $shipmentCode);
    }

    public function getFulfillments(int $orderCode, int $fulfillmentCode)
    {
        return $this->repository->getOrderFulfillments($orderCode, $fulfillmentCode);
    }

    public function getOrderFulfillment(int $orderCode, int $fulfillmentCode)
    {
        return $this->repository->getOrderFulfillment($orderCode, $fulfillmentCode);
    }

    public function createFulfillment(int $orderCode, Request $request): Response
    {
        $this->repository->startTransaction();
        try {
            $request->add('order_code', $orderCode);
            $this->validator->create()->validate($request);
            $validated = $this->validator->validated();
            $checkPersonalizedEmailVal = null;

            foreach ($validated['fulfillments'] as $fulfillment) {
                if (isset($fulfillment['shipment_code'])) {
                    throw new \Exception('Shipment Code is not allowed');
                }

                $lineItem = LineItem::where('line_item_code', '=', $fulfillment['line_item_code'])->first();

                if (!$lineItem) {
                    throw new \Exception('Line item' . $fulfillment['line_item_code'] . 'does not exist');
                }
                $fulfillment_type = $fulfillment['fulfillment_type'] ?? "DEFAULT";
            }

            $orderModel = $this->orderRepository->getOrderWithDynamicRelations($orderCode, $this->orderWithDynamicRelations);

            if (!array_key_exists("from_shopify", $validated)) {

                if (isset($orderModel['deliveryTemplate']['additional_services'])) {
                    //bringing company personalized messages (integration based fulfillment message)
                    $CompanyPersonalizeEmail = $this->CompanyPersonalizedEmailCreatedOrder($orderModel);

                    //iterating over the additional service of order template
                    foreach ($orderModel['deliveryTemplate']['additional_services'] as $key => $value) {
                        $checkPersonalizedEmailVal = ASCharge::with('additionalService')->where('as_charge_code', $value)->first();

                        //checking if personalized message exists and its addtional service code is simailar to env
                        if ($checkPersonalizedEmailVal && $checkPersonalizedEmailVal->additionalService->additional_service_code == env('PERSONALIZED_EMAIL_VAL')) {

                            // If no personalized message is set for the company
                            if (empty($CompanyPersonalizeEmail['personalizedMessage'])) {
                                $message = "Tracking Email message is not set up";

                                // Check if we can get the country name from the order model or retrieve it if necessary
                                $countryName = $this->getCountryName($orderModel, $orderCode);

                                // Set the response message, appending country name if available
                                $this->response->setData(['error' => $countryName ? "$message for $countryName" : $message]);
                                $this->response->setCode(400);
                                return $this->response;
                            }
                        }
                    }
                }
            }

            // Calculate the count of unique 'fulfillment_code' for fulfillments based on the type using the already-loaded relationships
            if (($fulfillment_type ?? '') === 'RETURN') {
                $fulfillmentsCount = $orderModel->fulfillmentReturns
                    ->unique('fulfillment_code')
                    ->count();
            } else {
                $fulfillmentsCount = $orderModel->fulfillments
                    ->unique('fulfillment_code')
                    ->count();
            }

            // Calculate the count of unique 'fulfillment_code' for cancelled fulfillments using the already-loaded relationship
            $cancelledFulfillmentsCount = $orderModel->cancelledFulfillments
                ->where('fulfillment_type', $fulfillment_type ?? 'DEFAULT')
                ->unique('fulfillment_code')
                ->count();

            $totalFulfillmentsCount = $fulfillmentsCount + $cancelledFulfillmentsCount + 1;
            $fulfillmentCode = generateHash();

            $fulfillmentName = $orderModel->order_number . ($fulfillment_type === 'RETURN' ? '-R-' : '-') . $totalFulfillmentsCount;
            $newFulfillmentArray = $validated['fulfillments'];
            foreach ($validated['fulfillments'] as $key => $fulfillment) {

                //updating the fulfilled quantity of each line item as it is fulfilled
                if (!isset($fulfillment['fulfillment_type']) || (isset($fulfillment['fulfillment_type']) && $fulfillment['fulfillment_type'] != "RETURN")) {
                    $lineItem = LineItem::where('line_item_code', '=', $fulfillment['line_item_code'])->update([
                        'fulfilled_quantity' => DB::raw('fulfilled_quantity + ' . $fulfillment['quantity'])
                    ]);
                }
                $fulfillment['order_code'] = $orderCode;
                $fulfillment['order_id'] = $orderModel->order_id;
                $fulfillment['integration_code'] = $orderModel->integration_code;
                $fulfillment['refund_id'] = $fulfillment['refund_id'] ?? null;
                $fulfillment['name'] = $fulfillmentName;
                $fulfillment['fulfillment_code'] = $fulfillmentCode;
                $fulfillment['packaging_code'] = $fulfillment['packaging_code'] ?? null;
                $fulfillment['reason'] = $fulfillment['reason'] ?? null;
                $fulfillment['comment'] = $fulfillment['comment'] ?? null;
                $fulfillment['fulfillment_type'] = $fulfillment['fulfillment_type'] ?? "DEFAULT";
                $fulfillment['reference'] = $fulfillment['reference'] ?? null;
                $this->repository->createFulfillment($fulfillment);

                //creating this array that has to be send to OrderRefundService
                $newFulfillmentArray[$key]['fulfillment_code'] = $fulfillmentCode;
                $newFulfillmentArray[$key]['price'] = $fulfillment['price'] ?? 0;
            }

            $message = (($fulfillment_type ?? '') == "RETURN") ? "Return fulfillment" : "fulfilled";
            if (($fulfillment_type ?? '') != "RETURN") {
                $this->orderService->updateOrderFulfillmentStatus($orderCode);
            }

            if ((($fulfillment_type ?? '') == "RETURN")) {
                $this->updateFulfillmentQuantity($validated['fulfillments']);
            }

            event(
                new SaveOrderEvent(
                    $orderCode,
                    'Order',
                    'fulfillment_success',
                    (Auth::user()->company->company_name ?? 'Shopify Admin') . ' ' . $message . ' ' . count($validated['fulfillments']) . ' items',
                    (Auth::user()->company->company_name ?? 'Shopify Admin') . ' ' . $message . ' ' . count($validated['fulfillments']) . ' items'
                )
            );

            if (($fulfillment_type ?? '') != "RETURN") {
                $this->orderService->fireCreateInvoiceEvent($orderModel, $fulfillmentCode, SalesInvoice::INVOICE_TYPE['INVOICE']);
            }

            if (!array_key_exists("from_shopify", $validated)) {
                $orderResponse = $this->orderService->show($request, $orderCode);
                try {
                    if (isset($orderModel['deliveryTemplate']) && $orderModel['deliveryTemplate']['additional_services'] != null) {
                        foreach ($orderModel['deliveryTemplate']['additional_services'] as $key => $value) {

                            if ($checkPersonalizedEmailVal && $checkPersonalizedEmailVal->additionalService->additional_service_code == env('PERSONALIZED_EMAIL_VAL')) {
                                $additionalServices[] = $value;
                                $orderModel['additional_services'] = $additionalServices;

                                // dont send personalized email to receiver on create return from merchant portal.
                                if ($fulfillment_type !== "RETURN") {
                                    event(new CreateOrderFulfillmentEvent($orderModel, 'fulfillment_created', $CompanyPersonalizeEmail));
                                }
                            }
                        }
                    }
                } catch (\Exception $e) {
                    info("Error while sending personalized email", [$e->getMessage()]);
                }
                $this->response->setData($orderResponse->getData());
            } else {
                $this->response->setData($newFulfillmentArray);
            }
        } catch (AuthenticationException $e) {
            $this->repository->rollbackTransaction();
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode(422);
        } catch (ValidationException $e) {
            $this->repository->rollbackTransaction();
            $this->response->setData(['error' => $e->errors()]);
            $this->response->setCode(422);
        } catch (\Exception $e) {
            $this->repository->rollbackTransaction();
            if ($e->getCode() == '23000') {
                info("Unique constraints for creating return");
                $this->response->setData(['error' => "Already Fulfilled Uniquely."]);
                $this->response->setCode(409);
            } else {
                info([$e]);
                $this->response->setData(['error' => $e->getMessage()]);
                $this->response->setCode(400);
            }
        }

        $this->repository->endTransaction();
        return $this->response;
    }

    private function getCountryName($orderModel, $orderCode)
    {
        // Check if customer and country data are already available
        if (isset($orderModel['customer']['country_name'])) {
            return $orderModel['customer']['country_name'];
        }

        if (!$orderModel['customer'] || !isset($orderModel['customer']['country_code'])) {
            // Fetch customer data from Shopify and refresh the order model
            $this->getOrderCustomerFromShopify($orderModel);
            $orderModel = $this->orderRepository->getOrderWithDynamicRelations($orderCode, $this->orderWithDynamicRelations);
        }

        // Get the country name by country code if it is now available
        if (isset($orderModel['customer']['country_code'])) {
            $country = $this->countryRepository->getCountryByReceiverCode($orderModel['customer']['country_code']);
            return $country['default_name'] ?? null;
        }

        return null;
    }

    public function getOrderCustomerFromShopify($order_data)
    {
        if ($order_data['customer'] == null) {
            $integration = \Main\Modules\Platforms\Models\Integration::where('integration_code', $order_data['integration_code'])
                ->whereNull('deleted_at')
                ->with('platform')->first();

            if (!isset($integration) || !$integration) {
                info("Integration not found in fulfillment service");
                throw new \Exception('Integration not found');
            }
            $platformService = app(EcommerceServiceInterface::class, ['platform' => $integration->platform->name]);

            $integrationData = $integration->toArray();
            $get_customer = $platformService->getCustomer($integrationData['api_url'], $order_data['order_id'], $integrationData['accesstoken']);
            $customer_data = $get_customer->toArray();
            $customer_array = $customer_data['customer'];
            $customer = new \Main\Modules\Order\Models\Customer();
            $customer->customer_code = generateHash();
            $customer->integration_code = $order_data['integration_code'];
            $customer->company_code = $order_data['company_code'];
            $customer->customer_id = $customer_array->id;
            $customer->email = $customer_array->email ?? null;
            $customer->accepts_marketing = $customer_array->accepts_marketing ?? 0;
            $customer->first_name = $customer_array->first_name ?? null;
            $customer->last_name = $customer_array->last_name ?? null;
            $customer->state = $customer_array->state ?? null;
            $customer->note = $customer_array->note ?? null;
            $customer->verified_email = $customer_array->verified_email;
            $customer->phone = $customer_array->phone ?? null;
            $customer->currency = $customer_array->currency ?? null;
            $customer->addresses = $customer_array->addresses ?? null;

            if (isset($customer_array->default_address)) {
                $customer->default_address = $customer_array->default_address ?? null;
                $customer->address1 = $customer_array->default_address->address1 ?? null;
                $customer->address2 = $customer_array->default_address->address2 ?? null;
                $customer->city = $customer_array->default_address->city ?? null;
                $customer->zipcode = $customer_array->default_address->zip ?? null;

                $country = $customer_array->default_address->country_name;
                $countryModel = Country::where('default_name', '=', $country)->first();

                if ($countryModel) {
                    $customer->country_code = $countryModel->country_code;
                }
            }
            $customer->save();

            $order = OrderModel::where('order_code', $order_data['order_code'])->first();
            if (!empty($order)) {
                $order->update(['customer_code' => $customer->customer_code]);
            }
        }
    }

    public function createFulfillmentRefund(int $orderCode, Request $request): Response
    {
        $this->repository->startTransaction();
        try {
            /** @var OrderRepository $orderRepository */
            $orderRepository = app(\Main\Modules\Order\Repositories\OrderRepository::class);
            $orderModel = $orderRepository->order($orderCode);

            $request->add('order_code', $orderCode);
            $this->validator->createRefund()->validate($request);
            $validated = $this->validator->validated();

            if (!array_key_exists("from_shopify", $validated)) {
                $this->authenticator->authenticate();
            }

            $lineItemArray = [];
            $totalAmount = 0.0;

            foreach ($validated['fulfillments'] as $returnOrderLineItem) {
                $totalAmount += $returnOrderLineItem['price'] * $returnOrderLineItem['quantity'];
                $lineItem = LineItem::where('line_item_code', '=', $returnOrderLineItem['line_item_code'])->first();

                $lineItemArray[] = [
                    'line_item_id' => $lineItem['line_item_id'],
                    'quantity' => $returnOrderLineItem['quantity'],
                ];
            }

            $order_id = $orderModel->order_id ?? null;
            $transaction_id = $validated['transaction_id'] ?? null;
            $refund_id = $validated['fulfillments'][0]['refund_id'] ?? null;

            $refundPaymentAmount = [];
            $totalAmount = $totalAmount + $validated['shipping_charges'];

            $refundPaymentAmount['refundPaymentAmount'] = $totalAmount;
            $fulfillmentsToBeUpdate['fulfillments'] = $validated['fulfillments'];

            $transactionIdArray['transaction_id'] = $validated['transaction_id'] ?? null;
            $transactionStatusArray['transaction_status'] = $validated['transaction_status'] ?? null;

            $this->domainRequest->set($refundPaymentAmount);
            $this->domainRequest->set($fulfillmentsToBeUpdate);
            $this->domainRequest->set($transactionIdArray);
            $this->domainRequest->set($transactionStatusArray);

            if (array_key_exists("from_shopify", $validated)) {
                $this->domainRequest->set(['from_shopify' => $validated['from_shopify']]);
            }

            $response = $this->orderService->refundPayment($orderCode, $this->domainRequest, $lineItemArray, $totalAmount);
            $refundsResponse = $response->getData();
            if ($refundsResponse !== null && isset($refundsResponse['error'])) {
                info("refund failed " . $refundsResponse['error']);
                throw new \Exception($refundsResponse['error'] ?? "Error found while refundPayment", 400);
            }

            if ($refundsResponse !== null) {
                if (isset($refundsResponse['refund_line_items.quantity'])) {
                    throw new \Exception($refundsResponse['refund_line_items.quantity'][0] ?? "cannot refund more items than were purchased", 400);
                }
            }

            $this->updateFulfillment($validated['fulfillments']);

            $shippingLines = [
                'title' => $orderModel->shipping_lines[0]['title'],
                'price' => $validated['shipping_charges'],
                'taxes_included' => isset($orderModel->shipping_lines[0]['tax_lines'][0]),
                'vat_rate' => isset($orderModel->shipping_lines[0]['tax_lines'][0]) ? $orderModel->shipping_lines[0]['tax_lines'][0]['rate'] : 0,
            ];


            $orderRefund = null;
            if (isset($order_id) && isset($transaction_id) && isset($refund_id)) {
                $orderRefund = OrderRefund::where('order_id', $order_id)
                    ->where('transaction_id',  $transaction_id)
                    ->where('refund_id', $refund_id)
                    ->first();
            }
            // if refund is from shopify dont generate credit note
            if (!array_key_exists("from_shopify", $validated)) {
                // refund_id & transaction_id (return from shopify response from refund api call) are required to fetch to fetch order refund,
                if (array_key_exists("refund_id", $refundsResponse) && $refundsResponse['refund_id'] != null) {
                    $order_id = $refundsResponse['order_id'];
                    $transaction_id = $refundsResponse['transaction_id'];
                    $refund_id = $refundsResponse['refund_id'];
                }

                if (isset($order_id) && isset($transaction_id) && isset($refund_id)) {

                    $orderRefund = OrderRefund::where('order_id', $order_id)
                        ->where('transaction_id',  $transaction_id)
                        ->where('refund_id', $refund_id)
                        ->first();

                    $orderRefundArray = [];
                    if (isset($orderRefund)) {
                        $orderRefundArray = [
                            'status' => $orderRefund->status ?? 'NonE',
                            'order_refund_code' => $orderRefund->order_refund_code ?? null,
                        ];
                    }

                    if ($validated['fulfillments'][0]['fulfillment_code']) {
                        $this->orderService->fireCreateReturnInvoiceEvent(
                            $orderModel,
                            $validated['fulfillments'][0]['fulfillment_code'],
                            SalesInvoice::INVOICE_TYPE['CREDIT_NOTE'],
                            $validated['fulfillments'],
                            $shippingLines,
                            $orderRefundArray,
                        );
                    }
                }
            }

            $userName = Auth::user()->company->company_name ?? 'Shopify Admin';
            $message = generateRefundMessage($userName, ($orderRefund->status ?? 'NoNe'), ($refundPaymentAmount['refundPaymentAmount'] ?? 0), $orderModel->total_price_set['presentment_money']['currency_code']);
            event(
                new SaveOrderEvent(
                    $orderCode,
                    'Order',
                    'fulfillment_refunded',
                    $message,
                    $userName . ' fulfillment_refunded ',
                    //storing order refund in event_id in events table, is used for fetching this event to generate new event for success with common information
                    isset($orderRefund) ? $orderRefund->order_refund_code : 24821
                )
            );

            $this->response->setData($response->toArray());
            $this->response->setCode(200);
        } catch (\Exception $e) {
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode(400);
        }
        $this->repository->endTransaction();
        return $this->response;
    }

    public function updateFulfillment($fulfillments)
    {
        foreach ($fulfillments as $fulfillment) {

            Fulfillment::where('line_item_code', $fulfillment['line_item_code'])
                ->where('fulfillment_code', $fulfillment['fulfillment_code'])->update([
                    'refunded' => 1,
                    'returned_quantity' => $fulfillment['quantity']
                ]);
        }
    }

    public function updateFulfillmentQuantity($fulfillments)
    {
        foreach ($fulfillments as $fulfillment) {
            Fulfillment::where('fulfillment_code', $fulfillment['reference'])
                ->where('line_item_code', $fulfillment['line_item_code'])
                ->where('fulfillment_type', 'DEFAULT')
                ->update([
                    'returned_quantity' => DB::raw('returned_quantity + ' . $fulfillment['quantity'])
                ]);
        }
    }


    public function CompanyPersonalizedEmailCreatedOrder($orderModel): ?Company
    {
        return Company::with([
            'personalizedMessage' => function ($query) use ($orderModel) {
                $query->whereHas('messageHook', function ($subquery) {
                    $subquery->where('content', 'Integration Based: Fulfillment Created');
                })->where(function ($subquery) use ($orderModel) {
                    $subquery->where([
                        ['receiver_country_code', '=', $orderModel['customer']->country_code],
                        ['is_integration_specific', '=', true],
                    ])->orWhere([
                        ['is_receiver_country_specific', '=', false],
                        ['is_integration_specific', '=', true],
                    ]);
                });
            }
        ])->where('company_code', Auth::user()->company_code)
            ->select(['company_code', 'id'])  // Select only necessary fields
            ->first();
    }

    public function find($fulfillment_code)
    {
        return $this->model->find($fulfillment_code);
    }
}
