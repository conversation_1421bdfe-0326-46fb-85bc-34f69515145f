<?php

namespace Main\Modules\Ecommerce\Shopify;

use Illuminate\Support\Facades\Auth;
use Main\Modules\Ecommerce\Generic\EcommerceProductServiceInterface;
use Main\Modules\Order\Models\LineItem;
use Main\Modules\Order\Models\Product;

class ShopifyProductService
{

    protected $model;
    protected int $platformCode;
    protected int $integrationCode;
    protected int $companyCode;
    protected array $product;
    protected array $variant;

    public function __construct(int $integrationCode, int $companyCode, $product, private ShopifyRepository $repository)
    {
        $this->integrationCode = $integrationCode;
        $this->companyCode = $companyCode;
        $this->product = $product;
    }


    /**
     * saves product
     *
     * @return bool
     */
    public function save(): void
    {
        $this->model = Product::where([
            ['integration_code', '=', $this->integrationCode],
            ['product_id', '=', $this->product['id']],
            ['company_code', '=', Auth::user()->company_code ?? $this->companyCode]
        ])->first();

        if ($this->model !== null) {
            $this->update();
        } else {
            $this->create();
        }
    }

    /**
     * Updates an product record
     *
     * @return bool
     */
    private function update(): bool
    {
        $this->populateModel();
        return $this->model->save();
    }

    /**
     * Creates an product record
     *
     * @return bool
     */
    private function create(): bool
    {
        $this->model = new Product();
        $this->model->product_code = generateHash();
        $this->model->integration_code = $this->integrationCode;
        $this->model->company_code = $this->companyCode;

        $this->populateModel();
        return $this->model->save();
    }

    /**
     * populates model with data from product array
     *
     * @return void
     */
    private function populateModel(): void
    {
        $this->model->product_id = $this->product['id'] ?? null;
        $this->model->title = $this->product['title'] ?? null;
        $this->model->body_html = $this->product['body_html'] ?? null;
        $this->model->vendor = $this->product['vendor'] ?? null;
        $this->model->handle = $this->product['handle'] ?? null;
        $this->model->product_status = $this->product['status'] ?? null;
        $this->model->variants = $this->product['variants'] ?? null;

        // $this->model->sku = $this->variant['sku'] ?? null;
        // $this->model->barcode = $this->product['variants'][0]['barcode'] ?? null;
        // $this->model->weight = $this->variant['grams'] / 1000 ?? null; // converting to KG
        // $this->model->selling_price = $this->variant['price'] ?? null;

        // if the product has a default image
        if (isset($this->product['image']) && isset($this->product['image']['src'])) {
            $this->model->image_url = $this->product['image']['src'];
        }

        //if the product has variant images and if those variant exists in our db then update their images too
        if (isset($this->product['images'])) {
            $this->model->images = $this->product['images'];
            $this->updateLineItemVariantImages($this->product['variants'], $this->product['images']);
        }

        $this->model->tags = $this->product['tags'] ?? null;
    }

    public function updateLineItemVariantImages($variants, $images)
    {
        //iterating over variants and the images array returned from the product
        foreach ($variants as $variant) {
            foreach ($images as $image) {
                //initially set the variant image to null and find it by iterating over variants and images
                //now compare the varint id with varinat_id in image record , if matched then update the line item vaiant image
                $variant_image = null;
                if (in_array($variant['id'], $image['variant_ids'])) {
                    $variant_image = $image['src'];

                    //get all of the line items of the matched variant and replace the image
                    $lienItems = LineItem::where('variant_id', $variant['id'])->get();
                    foreach ($lienItems as $lienItem) {
                        $newLineItem = $lienItem->variant;
                        $newLineItem['variant_image'] = $variant_image;
                        $lienItem->variant = $newLineItem;
                        $lienItem->update();
                    }
                    break;
                }
            }
        }
    }
}
