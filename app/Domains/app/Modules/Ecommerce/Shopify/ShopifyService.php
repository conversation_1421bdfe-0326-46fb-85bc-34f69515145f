<?php

namespace Main\Modules\Ecommerce\Shopify;

use Exception;
use Illuminate\Support\Facades\Log;
use Main\Modules\Ecommerce\Generic\EcommerceServiceInterface;

class ShopifyService implements EcommerceServiceInterface
{

    public function __construct(private ShopifyRepository $repository) {}

    public function getCustomer($shopUrl, $orderId, $accessToken)
    {
        return $this->repository->getCustomer($shopUrl, $orderId, $accessToken);
    }

    public function updateOrderShippingAddress($shopUrl, $accessToken, $orderId, $shippingAddress, $email)
    {
        return $this->repository->updateOrderShippingAddress($shopUrl, $accessToken, $orderId,  $shippingAddress, $email);
    }

    public function getSingleProduct($shopUrl, $accessToken, $productId)
    {
        return  $this->repository->getSingleProduct($shopUrl,  $accessToken, $productId);
    }

    public function calculateRefund($shopUrl, $accessToken, $orderID, $post_data)
    {
        return $this->repository->calculateRefund($shopUrl,  $accessToken, $orderID, $post_data);
    }

    public function createRefund($shopUrl, $accessToken, $orderID, $post_data)
    {
        return $this->repository->createRefund($shopUrl, $accessToken, $orderID, $post_data);
    }

    public function createFulfillment($shopUrl, $accessToken, $orderID, $post_data)
    {
        return $this->repository->createFulfillment($shopUrl, $accessToken, $orderID, $post_data);
    }

    public function getFulfillment($shopUrl, $accessToken, $fulfillmentId)
    {
        return $this->repository->getFulfillment($shopUrl, $accessToken, $fulfillmentId);
    }

    public function cancelFulfillment($shopUrl, $accessToken, $fulfillmentId)
    {
        return $this->repository->cancelFulfillment($shopUrl, $accessToken, $fulfillmentId);
    }

    public function getOrderFulfillment($shopUrl, $accessToken, $orderID)
    {
        return $this->repository->getOrderFulfillment($shopUrl, $accessToken, $orderID);
    }

    public function getOrderDetails($shopUrl, $accessToken, $orderID)
    {
        return $this->repository->getOrderDetails($shopUrl, $accessToken, $orderID);
    }

    public function getLineItemsFromFulfillmentOrder($shopUrl, $accessToken, $GrqphQLOrderID)
    {
        return $this->repository->getLineItemsFromFulfillmentOrder($shopUrl, $accessToken, $GrqphQLOrderID);
    }

    public function getFulfillmentOrderById($shopUrl, $accessToken, $GrqphQLOrderID)
    {
        return $this->repository->getFulfillmentOrderById($shopUrl, $accessToken, $GrqphQLOrderID);
    }

    public function getFulfillmentOrder($shopUrl, $accessToken, $GrqphQLOrderID)
    {
        return $this->repository->getFulfillmentOrder($shopUrl, $accessToken, $GrqphQLOrderID);
    }

    public function capturePayment($shopUrl, $accessToken, $orderID, $post_data)
    {
        return $this->repository->capturePayment($shopUrl, $accessToken, $orderID, $post_data);
    }
    public function getRemainingCapturableBalance($shopUrl, $accessToken, $orderID)
    {
        return $this->repository->getRemainingCapturableBalance($shopUrl, $accessToken, $orderID);
    }

    public function getOrderTransactions($shopUrl, $accessToken, $orderID)
    {
        return $this->repository->getOrderTransactions($shopUrl, $accessToken, $orderID);
    }

    public function markAsPaid($shopUrl, $accessToken, $orderID, $post_data)
    {
        return $this->repository->markAsPaid($shopUrl, $accessToken, $orderID, $post_data);
    }

    public function create_webhook($shop, $type, $callbacK_url, $format)
    {
        $shopUrl = $shop['api_url'];
        $accessToken = $shop['accesstoken'];
        $callbacK_url = env('WEBHOOK_CALLBACK_URL') . $callbacK_url;

        $post_data = array('topic' => $type, 'address' => $callbacK_url, 'format' => $format);
        try {
            $this->repository->createWebhook($shopUrl, $accessToken, $post_data);
            return true;
        } catch (\Exception $e) {
            Log::info("webhook failed for " . $e->getMessage());
            return false;
        }
    }
}
