<?php

namespace Main\Modules\Ecommerce\Shopify;

use Illuminate\Http\Request;
use Main\Events\SaveOrderEvent;
use Illuminate\Support\Facades\DB;
use Main\Modules\Order\Models\Order;
use Main\Modules\Order\Models\LineItem;
use Main\Modules\Order\Entities\Order as EntitiesOrder;
use Main\Modules\Ecommerce\Generic\EcommerceFulfillmentServiceInterface;
use Main\Modules\Order\Models\OrderRefund;

class ShopifyOrderRefundService
{
    public $fulfillmentRequest;

    public function __construct(public EcommerceFulfillmentServiceInterface $ecommerceFulfillmentService)
    {
        $this->fulfillmentRequest = app(\Main\Modules\Order\Requests\FulfillmentRequest::class);
    }

    public function handleRefundFromShopify($data)
    {
        $refund_id = $data['id'];
        $fulfillments = [];
        $singleItemPrice = 0.0;

        $orderCode = Order::where('order_id', $data['order_id'])->value('order_code');
        $totalQuantity = array_reduce($data['refund_line_items'], function ($carry, $item) {
            return $carry + $item['quantity'];
        }, 0);

        if (isset($data['transactions'][0]['amount']) && $totalQuantity > 0) {
            $singleItemPrice = ($data['transactions'][0]['amount'] / $totalQuantity);
        }

        try {
            foreach ($data['refund_line_items'] as $key => $item) {
                $lineItem = LineItem::where('line_item_id', $item['line_item_id'])->first();
                $fulfillment = DB::table('fulfillments')->where(['line_item_code' => $lineItem->line_item_code])->first();

                if ($fulfillment->refunded == 1) {
                    break;
                }

                $fulfillments[$key] = [
                    'fulfillment_type' => "RETURN",
                    'name' => $fulfillment->name ?? 'NaN FulFillment',
                    'reference' => $fulfillment->fulfillment_code,
                    'quantity' => $item['quantity'],
                    'price' => $singleItemPrice,
                    'line_item_code' => $lineItem->line_item_code,
                    'refund_id' => $refund_id,
                    'transaction_status' => $data['transactions'][0]['status'],
                ];
            }
        } catch (\Exception $e) {
            info("refund failed from shopify");
            info($e->getMessage());
        }

        $request = new Request();
        $request->merge(['fulfillments' => $fulfillments]);
        $request->merge(['from_shopify' => "true"]);
        $request->merge(['transaction_id' => $data['transactions'][0]['id']]);
        $request->merge(['transaction_status' => $data['transactions'][0]['status'] ?? 'SuCCeSS']);
        $request->merge(['shipping_charges' => 0.0]);
        $this->fulfillmentRequest->set($request->all());

        // creating return
        $createReturn = $this->ecommerceFulfillmentService->createFulfillment($orderCode, $this->fulfillmentRequest);
        $request->merge(['fulfillments' => $createReturn->getData()]);
        $this->fulfillmentRequest->set($request->all());

        // creating refund, if refund is not for line items then just add the amount to refund balance and change its status otherwise run complete flow for creating fulfillment
        if (empty($data['refund_line_items']) && isset($data['transactions'][0]['amount'])) {

            $orderRefund = OrderRefund::where([
                'transaction_id' => $data['transactions'][0]['id'],
                'order_code' => $orderCode
            ])->first();

            if (!$orderRefund) {
                $model = Order::where('order_code', '=', $orderCode)->first();
                $this->updateOrderStatus($model, $data['transactions'][0]['amount']);

                $message = generateRefundMessage('Shopify Admin', ($data['transactions'][0]['status'] ?? 'OnGoing'), ($data['transactions'][0]['amount'] ?? 0), $data['transactions'][0]['currency']);
                event(
                    new SaveOrderEvent(
                        $orderCode,
                        'Order',
                        'fulfillment_refunded',
                        $message,
                        'Shopify Admin fulfillment_refunded '
                    )
                );
            }
        } else {
            $this->ecommerceFulfillmentService->createFulfillmentRefund($orderCode, $this->fulfillmentRequest);
        }
    }

    public function updateOrderStatus($modal, $refundAmount)
    {
        $total_refund_amount =  (float) $modal->refund_amount + (float)$refundAmount;
        $modal->refund_amount = $total_refund_amount;

        if ($total_refund_amount == (float) $modal->total_price) {
            $modal->financial_status = EntitiesOrder::ORDER_STATUS['REFUNDED'];
        } else {
            $modal->financial_status = EntitiesOrder::ORDER_STATUS['PARTIALLY_REFUNDED'];
        }

        $modal->save();
    }
}
