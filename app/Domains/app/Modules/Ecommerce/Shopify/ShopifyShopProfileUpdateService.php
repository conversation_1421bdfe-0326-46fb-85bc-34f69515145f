<?php

namespace Main\Modules\Ecommerce\Shopify;

use Main\Modules\Location\Models\Country;
use Main\Modules\Platforms\Models\Integration;
use Main\Modules\Setting\Models\Currency;

class ShopifyShopProfileUpdateService
{
    public function updateShopProfile($shop_data)
    {
        if (!$shop_data || empty($shop_data)) {
            info("Shop data is not found from shopify request");
        } else {
            $shop = Integration::where(['myshopify_domain' => $shop_data['myshopify_domain'], 'shop_id' => $shop_data['id']])->whereNull('deleted_at')->first();

            if ($shop) {
                $shop->name = ucwords(str_replace('_', ' ', str_replace('-', ' ', $shop_data['name'])));
                $shop->email = $shop_data['email'];
                $shop->api_url = "https://" . $shop_data['myshopify_domain'] . '/';
                $shop->address = $shop_data['address1'];
                $shop->address_2 = $shop_data['address2'] ?? "";
                $shop->zipcode = $shop_data['zip'];
                $shop->city = $shop_data['city'];
                $shop->phone_no = $shop_data['phone'] ?? null;
                $shop->currency = $shop_data['currency'] ?? null;
                $shop->shop_id = $shop_data['id'] ?? null;
                $shop->myshopify_domain = $shop_data['myshopify_domain'] ?? null;
                $shop->country_code = Country::where("iso", $shop_data['country_code'])->first()->country_code ?? null;
                $shop->currency_code = Currency::where("short_form", $shop_data['currency'])->first()->currency_code ?? null;
                $shop->save();
            }
        }
    }
}
