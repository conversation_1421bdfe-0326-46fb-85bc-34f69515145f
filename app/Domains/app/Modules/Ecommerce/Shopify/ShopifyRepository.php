<?php

namespace Main\Modules\Ecommerce\Shopify;

use App\Utils\GraphQLResponseTransformer;
use App\Utils\WebhookTopicEnum;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\URL;

class ShopifyRepository
{
    use ShopifyRepositoryTrait;

    protected $shopDomain = null;
    private $client;
    protected $api_version = '2024-10';

    protected $responseHeaders = [];
    protected $requestHeaders = [];

    public function __construct()
    {
        $this->client = new Client();
    }

    public function getToken($shopUrl, $code)
    {
        return $this->setShopUrl($shopUrl)->getAccessToken($code);
    }

    /*
     *  $args[0] is for route uri and $args[1] is either request body or query strings
     */
    public function __call($method, $args)
    {
        if ($method == "create_webhook" && is_array($args[0])) {
            $this->setShopUrl($args[0]["api_url"]);
            $this->setAccessToken($args[0]["accesstoken"]);
            list($uri, $params) = [ltrim($args[0]["api_url"], "/"), $args[1] ?? []];
        } else {
            list($uri, $params) = [ltrim($args[0], "/"), $args[1] ?? []];
        }

        $response = $this->makeRequest($method, $uri, $params, $this->setXShopifyAccessToken());
        return (is_array($response)) ? $this->convertResponseToCollection($response) : $response;
    }


    /** graphQl Quries and mutations */
    private function executeQuery(string $query, ?array $variables = null)
    {
        try {
            $response = $this->glClient->query(['query' => $query, 'variables' => $variables]);

            if ($response->getStatusCode() === 200) {
                $graphQLResponse = $response->getDecodedBody();
                $data = GraphQLResponseTransformer::transform($graphQLResponse);
                if (isset($data['errors'])) {
                    throw new \Exception($data['errors'][0]['message']);
                }
                return $data;
            }

            Log::error("Shopify API Error", ['response' => $response->getDecodedBody()]);
            return $response->getDecodedBody();
        } catch (\Exception $e) {
            Log::error("Exception occurred during Shopify GraphQL query", ['message' => $e->getMessage()]);
            return ['error' => $e->getMessage()];
        }
    }

    public function getLineItemsFromFulfillmentOrder($shopUrl, $accessToken, $fulfillmentOrderId)
    {
        $this->initiliaze($shopUrl, $accessToken);

        $query = <<<'GRAPHQL'
        query GetFulfillmentOrderLineItems($id: ID!) {
            fulfillmentOrder(id: $id) {
                id
                order{
                    id
                }
                lineItems(first: 50) {
                    edges {
                        node {
                            id
                            lineItem {
                                id
                                name
                                sku
                                quantity
                                fulfillableQuantity
                            }
                        }
                    }
                }
            }
        }
        GRAPHQL;

        $variables = ['id' => $fulfillmentOrderId];

        return $this->executeQuery($query, $variables);
    }

    public function getFulfillmentOrderById($shopUrl, $accessToken, $fulfillmentOrderId)
    {
        $this->initiliaze($shopUrl, $accessToken);
        $query = <<<'GRAPHQL'
            query GetFulfillmentOrderDetails($fulfillmentOrderId: ID!) {
                fulfillmentOrder(id: $fulfillmentOrderId) {
                    id
                    status
                    assignedLocation {
                        location {
                            id
                            name
                        }
                    }
                    order {
                        id
                        name
                    }
                    lineItems(first: 10) {
                        edges {
                            node {
                                id
                                lineItem {
                                    id
                                    name
                                    quantity
                                }
                            }
                        }
                    }
                    fulfillments(first: 10) {
                        edges {
                            node {
                                id
                                status
                                trackingInfo {
                                    number
                                    url
                                }
                            }
                        }
                    }
                }
            }
        GRAPHQL;

        $variables = ['fulfillmentOrderId' => $fulfillmentOrderId];
        return $this->executeQuery($query, $variables);
    }

    public function getOrderFulfillment($shopUrl, $accessToken, $orderId)
    {
        $this->initiliaze($shopUrl, $accessToken);
        $query = <<<'GRAPHQL'
            query GetFulfillmentOrders($id: ID!) {
                order(id: $id) {
                    fulfillmentOrders(first: 50) {
                        edges {
                            node {
                                id
                                status
                                lineItems(first: 50) {
                                    edges {
                                        node {
                                            id
                                            lineItem {
                                                id
                                                name
                                                quantity
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        GRAPHQL;

        $variables = ['id' => "gid://shopify/Order/{$orderId}"];
        return $this->executeQuery($query, $variables);
    }

    public function getOrderDetails($shopUrl, $accessToken, $orderId)
    {
        $this->initiliaze($shopUrl, $accessToken);
        $query = <<<'GRAPHQL'
            query GetOrderDetails($id: ID!) {
                order(id: $id) {
                    id
                    name
                    displayFinancialStatus
                }
            }
        GRAPHQL;

        $variables = ['id' => "gid://shopify/Order/{$orderId}"];
        return $this->executeQuery($query, $variables);
    }

    public function createFulfillment($shopUrl, $accessToken, $orderId, $postData)
    {
        $this->initiliaze($shopUrl, $accessToken);
        $query = <<<QUERY
        mutation FulfillmentCreate(\$fulfillment: FulfillmentInput!) {
            fulfillmentCreate(fulfillment: \$fulfillment) {
                fulfillment {
                    id
                    status
                }
                userErrors {
                    field
                    message
                }
            }
        }
        QUERY;

        $variables = [
            "fulfillment" => [
                "lineItemsByFulfillmentOrder" => [
                    "fulfillmentOrderId" => "gid://shopify/FulfillmentOrder/{$orderId}",
                    "fulfillmentOrderLineItems" => array_map(function ($item) {
                        return [
                            "id" => "gid://shopify/FulfillmentOrderLineItem/{$item['line_item_id']}",
                            "quantity" => $item['quantity'],
                        ];
                    }, $postData),
                ],
            ],
        ];

        return $this->executeQuery($query, $variables);
    }

    public function getFulfillment($shopUrl, $accessToken, $fulfillmentId)
    {
        $this->initiliaze($shopUrl, $accessToken);
        $query = <<<QUERY
        query fulfillment(\$id: ID!) {
            fulfillment(id: \$id) {
                id
                status
            }
        }
        QUERY;

        $variables = [
            "id" => "gid://shopify/Fulfillment/{$fulfillmentId}"
        ];

        return $this->executeQuery($query, $variables);
    }

    public function cancelFulfillment($shopUrl, $accessToken, $fulfillmentId)
    {
        $this->initiliaze($shopUrl, $accessToken);
        $query = <<<QUERY
        mutation fulfillmentCancel(\$id:ID!){
            fulfillmentCancel(id: \$id){
                fulfillment{
                    id
                    status
                }
                userErrors{
                    field
                    message
                }
            }
        }
        QUERY;
        $variables = [
            "id" => "gid://shopify/Fulfillment/{$fulfillmentId}"
        ];

        return $this->executeQuery($query, $variables);
    }

    /**
     * Summary of getShopData
     * @return mixed|string
     */
    public function getShopData(string $shopUrl, string $accessToken): mixed
    {
        $this->initiliaze($shopUrl, $accessToken);

        $fragments = $this->loadFragments(['shop']);

        $query = <<<QUERY
            query GetShopDetails {
                 shop {
                   ...ShopDetails
                }
            }
         $fragments
        QUERY;

        $data = $this->executeQuery($query);
        return $data['data']['shop'];
    }

    public function getCustomer($shopUrl, $orderId, $accessToken)
    {
        return $this->setShopUrl($shopUrl)->setAccessToken($accessToken)->get("/admin/api/$this->api_version/orders/$orderId" . ".json?fields=id%2Ccustomer");
    }

    public function fetchWebhooks($shopUrl, $accessToken)
    {
        $this->initiliaze($shopUrl, $accessToken);

        $query = <<<QUERY
        query {
          webhookSubscriptions(first: 250) {
            edges {
              node {
                id
                topic
                callbackUrl
                endpoint {
                  __typename
                  ... on WebhookHttpEndpoint {
                    callbackUrl
                  }
                  ... on WebhookEventBridgeEndpoint {
                    arn
                  }
                  ... on WebhookPubSubEndpoint {
                    pubSubProject
                    pubSubTopic
                  }
                }
              }
            }
          }
        }
      QUERY;
        $data = $this->executeQuery($query);
        $webhooks = $data['data']['webhook_subscriptions'];
        foreach ($webhooks as &$webhook) {
            $webhook['topic'] = WebhookTopicEnum::convertTopicEnumToRestFormat($webhook['topic']);
        }
        return $webhooks;
    }


    public function createWebhook($shopUrl, $accessToken, array $data)
    {
        $this->initiliaze($shopUrl, $accessToken);

        $query = <<<QUERY
            mutation webhookSubscriptionCreate(\$topic: WebhookSubscriptionTopic!, \$webhookSubscription: WebhookSubscriptionInput!) {
                webhookSubscriptionCreate(topic: \$topic, webhookSubscription: \$webhookSubscription) {
                webhookSubscription {
                    id
                    topic
                    filter
                    format
                    endpoint {
                    __typename
                    ... on WebhookHttpEndpoint {
                        callbackUrl
                    }
                    }
                }
                userErrors {
                    field
                    message
                }
                }
            }
            QUERY;

        $variables = [
            "topic" => $data['topic'],
            "webhookSubscription" => [
                "callbackUrl" => $data['address'],
                "format" => $data['format'],
            ],
        ];

        return  $this->executeQuery($query, $variables);
    }

    /**
     * Summary of createShippingCarrier
     * @param string $shopUrl
     * @param string $accessToken
     * @return mixed
     */
    public function createShippingCarrier(string $shopUrl, string $accessToken)
    {
        $this->initiliaze($shopUrl, $accessToken);

        $query = <<<QUERY
            mutation CarrierServiceCreate(\$input: DeliveryCarrierServiceCreateInput!) {
                carrierServiceCreate(input: \$input) {
                    carrierService {
                        id
                        name
                        callbackUrl
                        active
                    }
                    userErrors {
                        field
                        message
                    }
                }
            }
        QUERY;


        $variables = [
            "input" => [
                "name" => "Custom Shipping Rate",
                "callbackUrl" => URL::to('/') . '/api/get-shipping-methods',
                "supportsServiceDiscovery" => true,
                "active" => true,
            ],
        ];

        return $this->executeQuery($query, $variables);
    }


    public function getSingleProduct(string $shopUrl, string $accessToken, string|int $productId)
    {
        $this->initiliaze($shopUrl, $accessToken);
        $fragments = $this->loadFragments(['product']);
        $query = <<<QUERY
            query GetProductById(\$id: ID!) {
                product(id: \$id) {
                    ...ProductDetails
                }
            }
            $fragments
        QUERY;

        $data = $this->executeQuery($query, ['id' => "gid://shopify/Product/{$productId}"]);
        if (isset($data['data'])) {
            return GraphQLResponseTransformer::transformProduct($data['data']['product']);
        }
        return $data;
    }

    public function updateOrderShippingAddress($shopUrl, $accessToken, mixed $orderId, array $shippingAddress, ?string $email)
    {
        $this->initiliaze($shopUrl, $accessToken);

        $query = <<<QUERY
            mutation OrderUpdate(\$input: OrderInput!) {
                orderUpdate(input: \$input) {
                    order {
                        id
                        email
                    }
                    userErrors {
                        field
                        message
                    }
                }
            }
            QUERY;

        $input = [
            'id' => "gid://shopify/Order/{$orderId}",
        ];

        if (count($shippingAddress) > 0) {
            $input['shippingAddress'] = $shippingAddress;
        }

        // Add email only if provided
        if (!empty($email)) {
            $input['email'] = $email;
        }

        $variables = ['input' => $input];
        return $this->executeQuery($query, $variables);
    }

    public function capturePayment($shopUrl, $accessToken, mixed $orderId, $data)
    {
        $this->initiliaze($shopUrl, $accessToken);

        $query = <<<QUERY
            mutation orderCapture(\$input: OrderCaptureInput!) {
                orderCapture(input: \$input) {
                    transaction {
                        id
                        kind
                        gateway
                        status
                        test
                        amountSet {
                            presentmentMoney {
                                amount
                                currencyCode
                            }
                            shopMoney {
                                amount
                                currencyCode
                            }
                        }
                        paymentDetails
                        parentTransaction {
                            id
                        }
                        order{
                            displayFinancialStatus
                            totalOutstandingSet {
                            presentmentMoney {
                                amount
                                currencyCode
                            }
                            shopMoney {
                                amount
                                currencyCode
                            }
                        }
                        }
                    }
                    userErrors {
                        field
                        message
                    }
                }
            }
        QUERY;

        $input = [
            'id' => "gid://shopify/Order/{$orderId}",
            'parentTransactionId' => "gid://shopify/OrderTransaction/" . $data['transactionId'],
            'amount' =>  $data['amount'],
            'currency' =>  $data['currency_code'],
        ];

        $variables = ['input' => $input];
        return $this->executeQuery($query, $variables);
    }

    public function getRemainingCapturableBalance($shopUrl, $accessToken, mixed $orderId)
    {
        $this->initiliaze($shopUrl, $accessToken);
        $query = <<<'GRAPHQL'
            query getOrderDetails($id: ID!) {
                order(id: $id) {
                    id
                    capturableTransactions: transactions(first: 10, capturable: true) {
                        id
                        totalUnsettledSet {
                            presentmentMoney {
                                amount
                                currencyCode
                            }
                        }
                    }
                }
            }
        GRAPHQL;

        $variables = ['id' => "gid://shopify/Order/{$orderId}"];
        return $this->executeQuery($query, $variables);
    }

    public function getOrderTransactions($shopUrl, $accessToken, mixed $orderId)
    {
        $this->initiliaze($shopUrl, $accessToken);
        $query = <<<'GRAPHQL'
            query TransactionsForOrder($orderId: ID!) {
                order(id: $orderId) {
                    transactions(first: 1000) {
                        id
                        kind
                        gateway
                        status
                        test
                        authorizationExpiresAt
                        amountSet {
                            presentmentMoney {
                                amount
                                currencyCode
                            }
                            shopMoney {
                                amount
                                currencyCode
                            }
                        }
                        paymentDetails
                        parentTransaction {
                            id
                        }
                    }
                }
            }
        GRAPHQL;

        $variables = ['orderId' => "gid://shopify/Order/{$orderId}"];
        return $this->executeQuery($query, $variables);
    }

    public function markAsPaid($shopUrl, $accessToken, mixed $orderId, $data)
    {
        $this->initiliaze($shopUrl, $accessToken);

        $query = <<<QUERY
            mutation orderMarkAsPaid(\$input: OrderMarkAsPaidInput!) {
                orderMarkAsPaid(input: \$input) {
                    order {
                        id
                        displayFinancialStatus
                    }
                    userErrors {
                    field
                    message
                    }
                }
            }
        QUERY;

        $input = [
            'id' => "gid://shopify/Order/{$orderId}",
        ];

        $variables = ['input' => $input];
        return $this->executeQuery($query, $variables);
    }

    public function calculateRefund($shopUrl, $accessToken, $orderId, $postData)
    {
        $this->initiliaze($shopUrl, $accessToken);
        $query = <<<'GRAPHQL'
            query getOrderDetails($id: ID!) {
                order(id: $id) {
                    fulfillments {
                            status
                            createdAt
                            trackingInfo {
                                number
                                url
                            }
                            location{
                                id
                            }
                            fulfillmentLineItems(first: 10) {
                                edges {
                                    node {
                                        lineItem {
                                            name
                                            quantity
                                        }
                                    }
                                }
                            }
                        }
                        lineItems(first: 10) {
                            edges {
                                node {
                                    id
                                    title
                                    quantity
                                    refundableQuantity
                                }
                            }
                        }
                    transactions {
                        id
                        status
                        kind
                        parentTransaction {
                            id
                        }
                    }
                    currentTotalPriceSet {
                        shopMoney {
                            amount
                            currencyCode
                        }
                    }
                    suggestedRefund{
                        maximumRefundableSet{
                            presentmentMoney{
                                amount
                                currencyCode
                            }
                        }
                    }
                }
            }
        GRAPHQL;

        $variables = ['id' => "gid://shopify/Order/{$orderId}"];
        return $this->executeQuery($query, $variables);
    }

    public function createRefund($shopUrl, $accessToken, $orderId, $postData)
    {
        $this->initiliaze($shopUrl, $accessToken);
        $query = <<<'GRAPHQL'
        mutation refundCreate($input: RefundInput!) {
            refundCreate(input: $input) {
                refund {
                    id
                    totalRefundedSet {
                        presentmentMoney {
                            amount
                            currencyCode
                        }
                    }
                    transactions(first: 10) {
                        edges {
                            node {
                                id
                                amount
                                kind
                                status
                                gateway
                                test
                                order {
                                    id
                                }
                            }
                        }
                    }
                }
                userErrors {
                    field
                    message
                }
            }
        }
        GRAPHQL;

        info("postData inside shopify repository");
        info($postData);
        if ($postData['line_items']) {
            $postData['line_items'] = array_map(function ($item) {
                $restockType = $item['restock_type'] ?? 'RETURN';

                $lineItem = [
                    "lineItemId" => "gid://shopify/LineItem/{$item['line_item_id']}",
                    "quantity" => $item['quantity'],
                    "restockType" => $restockType,
                ];

                // Only include locationId if restockType is RETURN
                if ($restockType === 'RETURN' && isset($item['location_id'])) {
                    $lineItem["locationId"] = "gid://shopify/Location/{$item['location_id']}";
                }

                return $lineItem;
            }, $postData['line_items']);
        }

        $variables = [
            'input' => [
                'orderId' => "gid://shopify/Order/{$orderId}",
                'currency' => $postData['currency'],
                'refundLineItems' => $postData['line_items'] ?? [],
                "transactions" => [
                    [
                        'orderId' => "gid://shopify/Order/{$orderId}",
                        "gateway" => "manual",
                        "kind" => "REFUND",
                        "amount" => $postData['amount'],
                        "parentId" => $postData['parent_id'],
                    ]
                ]
            ]
        ];

        if ($postData['shipping_charges'] > 0) {
            $variables['input']['shipping'] = [
                "amount" => $postData['shipping_charges']
            ] + ($postData['shipping_charges_full_refund'] ? ["fullRefund" => true] : []);
        }

        return $this->executeQuery($query, $variables);
    }
}
