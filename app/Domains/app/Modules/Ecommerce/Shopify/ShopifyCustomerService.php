<?php

namespace Main\Modules\Ecommerce\Shopify;

use Main\Modules\Location\Models\Country;
use Main\Modules\Order\Models\Customer;

class ShopifyCustomerService
{
    protected $model;
    protected int $platformCode;
    protected int $integrationCode;
    protected int $companyCode;
    protected array $customer;

    public function __construct(int $integrationCode, int $companyCode, $customer, private ShopifyRepository $repository)
    {
        $this->integrationCode = $integrationCode;
        $this->companyCode = $companyCode;
        $this->customer = $customer;
    }


    /**
     * saves customer
     *
     * @return bool
     */
    public function save(): bool
    {
        if (($this->model = Customer::where([['customer_id', '=', $this->customer['id']], ['integration_code', '=', $this->integrationCode]])->first()) !== null) {
            return $this->update();
        }

        return $this->create();
    }

    /**
     * Updates an customer record
     *
     * @return bool
     */
    private function update(): bool
    {
        $this->populateModel();
        return $this->model->save();
    }

    /**
     * Creates an customer record
     *
     * @return bool
     */
    private function create(): bool
    {
        $this->model = new Customer();
        $this->model->customer_code = generateHash();
        $this->model->integration_code = $this->integrationCode;
        $this->model->company_code = $this->companyCode;

        $this->populateModel();
        return $this->model->save();
    }

    /**
     * populates model with data from customer array
     *
     * @return void
     */
    private function populateModel(): void
    {
        $this->model->customer_id = $this->customer['id'];
        $this->model->email = $this->customer['email'] ?? null;
        $this->model->accepts_marketing = $this->customer['accepts_marketing'] ?? 0;
        $this->model->first_name = $this->customer['first_name'] ?? null;
        $this->model->last_name = $this->customer['last_name'] ?? null;
        $this->model->state = $this->customer['state'] ?? null;
        $this->model->note = $this->customer['note'] ?? null;
        $this->model->verified_email = $this->customer['verified_email'];
        $this->model->phone = $this->customer['phone'] ?? null;
        $this->model->currency = $this->customer['currency'] ?? null;
        $this->model->addresses = $this->customer['addresses'] ?? null;

        if (isset($this->customer['default_address'])) {
            $this->model->default_address = $this->customer['default_address'];
            $this->model->address1 = $this->customer['default_address']['address1'] ?? null;
            $this->model->address2 = $this->customer['default_address']['address2'] ?? null;
            $this->model->city = $this->customer['default_address']['city'] ?? null;
            $this->model->zipcode = $this->customer['default_address']['zip'] ?? null;

            $country = $this->customer['default_address']['country'];
            $countryModel = Country::where('default_name', '=', $country)->first();

            if ($countryModel) {
                $this->model->country_name = $countryModel->default_name ?? $this->customer['default_address']['country'] ?? null;
                $this->model->country_code = $countryModel->country_code;
            }
        }

        $this->model->tags = $this->customer['tags'];
    }
}
