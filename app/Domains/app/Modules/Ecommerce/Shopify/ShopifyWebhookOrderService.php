<?php

namespace Main\Modules\Ecommerce\Shopify;

use Carbon\Carbon;
use Main\Events\SaveOrderEvent;
use Main\Services\ProductService;
use Illuminate\Support\Facades\Log;
use Main\Modules\Order\Models\Event;
use Main\Modules\Order\Models\Order;
use Main\Modules\Order\Models\Product;
use Main\Modules\Order\Models\Customer;
use Main\Modules\Order\Models\LineItem;
use Main\Modules\Company\Models\Company;
use Main\Modules\Order\Models\OrderRisk;
use Main\Modules\Location\Models\Country;
use Main\Modules\Order\Models\OrderRefund;
use Main\Modules\Shipment\Models\Template;
use Main\Modules\Order\Models\SalesInvoice;
use Main\Modules\Platforms\Models\Integration;
use Main\Modules\Order\Models\OrderTransaction;
use Main\Modules\Shipment\Models\CarrierProduct;
use Main\Modules\Platforms\Models\DeliveryMethod;
use Main\Modules\Setting\Requests\PrintJobRequest;
use Main\Modules\Setting\Services\PrintJobService;
use Main\Modules\Shipment\Services\TemplateService;
use Main\Modules\Order\Repositories\OrderRepository;
use Main\Modules\ShippingMethod\Models\ShippingMethod;
use Main\Modules\Order\Entities\Order as EntitiesOrder;
use Main\Modules\Order\Repositories\LineItemRepository;
use Main\Modules\Setting\Repositories\PrinterRepository;
use Main\Modules\Ecommerce\Generic\EcommerceServiceInterface;
use Main\Modules\Order\Services\OrderService as ServicesOrderService;

class ShopifyWebhookOrderService
{

    protected $model;
    protected int $platformCode;
    protected int $integrationCode;
    protected int $companyCode;
    protected array $order;
    public $lineItemRepository;
    public $isNewOrder = false;
    public $isWeightChanged = false;

    /**
     * Create a new template service.
     *
     * @return TemplateService
     */

    private TemplateService $templateService;


    public function __construct(int $platformCode, int $integrationCode, int $companyCode, $order)
    {
        $this->platformCode = $platformCode;
        $this->integrationCode = $integrationCode;
        $this->companyCode = $companyCode;
        $this->order = $order;
        $this->templateService = app(\Main\Modules\Shipment\Services\TemplateService::class);
        $this->lineItemRepository = app(LineItemRepository::class);
    }

    public function save(): bool
    {
        $status = true;
        try {
            $this->model = Order::where([
                ['integration_code', '=', $this->integrationCode],
                ['order_id', '=', $this->order['order_id'] ?? $this->order['id']]
            ])->withTrashed()->first();

            if (!$this->model) {
                info("use less checking");
                $companyOrder = Order::where([
                    ['company_code', '=', $this->companyCode],
                    ['order_id', '=', $this->order['order_id'] ?? $this->order['id']]
                ])->withTrashed()->first();

                if ($companyOrder) {
                    $isIntegrationFound = Integration::where('company_code', $this->companyCode)
                        ->where('integration_code', $companyOrder->integration_code)
                        ->withTrashed()->exists();

                    if ($isIntegrationFound) {
                        $companyOrder->update(['integration_code' => $this->integrationCode]);
                        $this->model = Order::where([
                            ['integration_code', '=', $this->integrationCode],
                            ['order_id', '=', $this->order['order_id'] ?? $this->order['id']]
                        ])->withTrashed()->first();
                    }
                }
            }

            if ($this->model !== null) {
                info("Order already exists");
                $this->update();
                $this->handleRefundStatusFromShopify();
            } else {
                info("Order does not exist");
                $this->create();
            }

            $this->saveEvents();
            $this->saveLineItems();
            $this->saveOrderRisks();
            $this->assignAutoTemplate();
            $this->saveOrderTransactions();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

        /**
         * This is placed here instead of at below of $this->create() because
         * order details are fetched after the initial order is created.
         *
         * Also this is not placed in the below of try block of try catch on the top
         * because of the transaction is created in the try block.
         *
         * So, this is placed here to make sure that the transaction is committed
         * before the below code is executed and transaction is not rolled back
         * if any exception fires in the below code.
         */

        try {
            if (
                isset($this->order['line_items']) &&
                !isset($this->model['pick_list_base64']) &&
                $this->model['is_printed_pick_list'] == 0
            ) {
                $this->createPrintJobForPickListOfOrder();
            }
        } catch (\Exception $e) {
            error_log($e->getMessage());
            error_log((string)$e->getLine());
        }

        return $status;
    }

    /**
     * Updates an order record
     *
     * @return bool
     */
    private function update(): bool
    {
        $this->populateModel();
        return $this->model->save();
    }

    /**
     * populates model with data from order array
     *
     * @return void
     */
    private function populateModel(): void
    {
        try {
            $this->model->ordered_at = $this->order['created_at'] ?? Carbon::now();
            $this->model->order_id = $this->order['order_id'] ?? $this->order['id'];
            $this->model->order_number = $this->order['order_number'];
            $this->model->name = $this->order['name'] ?? $this->order['order_number'];
            $this->model->location_id = $this->order['location_id'] ?? null;
            $this->model->financial_status = $this->getOrdeFinancialStatus();
            $this->model->subtotal_price = $this->order['subtotal_price'];
            $this->model->total_price = $this->order['total_price'];
            $this->model->total_price_set = $this->order['total_price_set'];
            $this->model->total_outstanding = $this->order['total_outstanding'] ?? null;
            $this->model->payment_terms = $this->order['payment_terms'] ?? null;

            // if the order is already created then dont change its billing address information
            if (!isset($this->model->billing_address) && isset($this->order['billing_address'])) {
                $billing_address = $this->order['billing_address'];
                $billing_address['email'] = $this->order['customer']['email'] ?? '';

                if (isset($billing_address['phone'])) {
                    $mobilePayload = $this->validatePhoneNumber($billing_address);
                    $billing_address['phone'] = $mobilePayload['phone'] ?? null;
                    $billing_address['dialCode'] = $mobilePayload['dialCode'] ?? null;
                }

                $this->model->billing_address = $billing_address;
            }

            if (isset($this->platformCode)) {
                $this->model->platform_code = $this->platformCode;
            }

            if (isset($this->order['customer']) && count($this->order['customer']) > 0 && isset($this->order['customer']['id'])) {
                $this->model->customer_code = $this->getCustomerCode($this->order['customer']['id']) ?? null;
            }

            if (isset($this->order['shipping_address'])) {
                $shippingAddress = $this->order['shipping_address'];

                if (isset($shippingAddress['phone'])) {
                    $mobilePayload = $this->validatePhoneNumber($shippingAddress);
                    $shippingAddress['phone'] = $mobilePayload['phone'] ?? null;
                    $shippingAddress['dialCode'] = $mobilePayload['dialCode'] ?? null;
                }
                $shippingAddress['email'] = $this->order['email'] ?? $this->order['customer']['email'] ?? '';

                if (isset($shippingAddress) && isset($this->model->order_code) && isset($this->model->shipping_address)) {
                    $this->verifyRecipientAddressChanges($shippingAddress, $this->model->shipping_address);
                }
                $this->model->shipping_address = $shippingAddress;
            }

            if (!isset($this->model->total_weight)) {
                if (isset($this->order['shipping_lines']) && count($this->order['shipping_lines']) && isset($this->order['shipping_lines'][0]['id'])) {
                    //this function is used to check, the delivery template against the shipping rule that is selected at checkout page
                    //if the method already exists use it otherwise create a new one

                    $deliveryMethod = $this->deliveryMethod($this->order['shipping_lines'][0]);
                    $parcelShop = extraParcelShopFromShippingLine($this->order['shipping_lines'][0]['title']);

                    //check if $deliveryTitle exists is returned by the deliveryMethod function
                    $this->order['shipping_lines'][0]['delivery_method_title'] = $deliveryMethod->name . (($parcelShop) ? (' - ' .  $parcelShop) : '');

                    //assign the delivery method to the order, returned by the above function
                    $this->model->delivery_method_code = $deliveryMethod->delivery_method_code;
                    $this->model->delivery_template_code = $deliveryMethod->delivery_template_code ?? null;
                    $this->model->return_template_code = $deliveryMethod->return_template_code ?? null;
                }
            }

            $this->model->shipping_lines = $this->order['shipping_lines'] ?? null;
            $this->model->subtotal_price_set = $this->order['subtotal_price_set'];
            $this->model->taxes_included = $this->order['taxes_included'];
            $this->model->tax_lines = $this->order['tax_lines'];
            $this->model->notes = $this->order['note'] ?? null;
            $this->model->note_attributes = $this->order['note_attributes'] ?? null;
            $this->model->refunds = $this->order['refunds'] ?? null;
            $this->model->cancel_reason = $this->order['cancel_reason'] ?? null;
            $this->model->cancelled_at = $this->order['cancelled_at'] ?? null;
            $this->model->closed_at = $this->order['closed_at'] ?? null;
            $this->model->current_subtotal_price = $this->order['current_subtotal_price'] ?? null;
            $this->model->current_subtotal_price_set = $this->order['current_subtotal_price_set'] ?? null;
            $this->model->current_total_discounts = $this->order['current_total_discounts'] ?? null;
            $this->model->discount_applications = $this->order['discount_applications'] ?? null;
            $this->model->total_discounts = $this->order['total_discounts'] ?? null;
            $this->model->total_discounts_set = $this->order['total_discounts_set'] ?? null;
            $this->model->current_total_discounts_set = $this->order['current_total_discounts_set'] ?? [];
            $this->model->current_total_duties_set = $this->order['current_total_duties_set'] ?? null;
            $this->model->current_total_price = $this->order['current_total_price'] ?? null;
            $this->model->current_total_price_set = $this->order['current_total_price_set'] ?? [];
            $this->model->current_total_tax = $this->order['current_total_tax'] ?? null;
            $this->model->current_total_tax_set = $this->order['current_total_tax_set'] ?? null;
            $this->model->total_shipping_price_set = $this->order['total_shipping_price_set'] ?? null;
            if ($this->model->total_weight != $this->order['total_weight']) {
                $this->isWeightChanged = true;
            }
            $this->model->total_weight = $this->order['total_weight'] ?? null;
            $this->model->payment_gateway_names = $this->order['payment_gateway_names'] ?? null;

            if (isset($this->order['cancel_reason']) && $this->order['cancel_reason'] != null) {
                $this->model->order_status = EntitiesOrder::ORDER_STATUS['CANCELLED'];
            } elseif (isset($this->order['closed_at']) && $this->order['closed_at'] !== null) {
                $this->model->order_status = EntitiesOrder::ORDER_STATUS['CLOSED'];
            } else {
                $this->model->order_status = EntitiesOrder::ORDER_STATUS['OPEN'];
            }
        } catch (\Exception $exception) {
            info("Exception found in populateModel");
            info($exception->getMessage());
        }
    }

    public function getOrdeFinancialStatus()
    {
        info("calling api");
        // we will get order financial status through graphQl to fetch latest status
        $shopifyService = app(EcommerceServiceInterface::class, ['platform' => "shopify"]);
        $response = $shopifyService->getOrderDetails(
            $this->model->integration->myshopify_domain,
            $this->model->integration->accesstoken,
            $this->model->order_id
        );

        $stat = $response['data']['order']['display_financial_status'] ?? null;
        info($stat);

        return $response['data']['order']['display_financial_status'] ?? null;
    }

    public function validatePhoneNumber($data)
    {
        $phoneNumber = $data['phone'] ?? null;
        $payLoad['phone'] = $phoneNumber;

        if (isset($phoneNumber)) {
            $DbCountry = isset($data['country_code'])
                ? Country::where('iso', $data['country_code'])->orWhere('iso3', $data['country_code'])->first()
                : Country::where('name', $data['country'])->first();

            $dialCode = $DbCountry ? $DbCountry->phone_code : null;

            if (isset($dialCode)) {
                $normalizedPhoneNumber = preg_replace('/[^\d]/', '', $phoneNumber);

                // if phone number has 00 infront of dial code, remove it
                if (strpos($normalizedPhoneNumber, "00$dialCode") === 0) {
                    $normalizedPhoneNumber = substr($normalizedPhoneNumber, 2);
                }

                $normalizedDialCode = preg_replace('/[^\d]/', '', "$dialCode");

                if (strpos($normalizedPhoneNumber, $normalizedDialCode) === 0) {
                    $phoneNumber = substr($normalizedPhoneNumber, strlen($normalizedDialCode));
                }
            }

            $payLoad['phone'] = $phoneNumber;
            $payLoad['dialCode'] = isset($dialCode) ? "+$dialCode" : "";
        }

        return $payLoad;
    }

    public function getCustomerCode(int $customerID): int|null
    {
        $model = Customer::where([['integration_code', '=', $this->integrationCode], ['customer_id', '=', $customerID]])->first();

        return $model ? $model->customer_code : null;
    }

    public function verifyRecipientAddressChanges($shippingAddress, $oldShippingAddress)
    {
        $adressChanged = false;
        foreach ($shippingAddress as $key => $value) {
            if (
                in_array($key, ['name', 'email', 'country_code', 'address1', 'address2', 'country', 'zip', 'city', 'phone', 'dialCode']) &&
                isset($oldShippingAddress[$key]) &&
                $value != $oldShippingAddress[$key]
            ) {
                $adressChanged = true;
                break;
            }
        }

        if ($adressChanged) {
            $this->createOrderEvent($this->model->order_code, "Order", "order_edited", "Shopify Admin updated the recipient address.", "Shopify Admin");
        }
    }

    public function createOrderEvent($orderCode, $subject, $verb, $message, $author)
    {
        event(
            new SaveOrderEvent(
                $orderCode,
                $subject,
                $verb,
                $message,
                $author
            )
        );
    }

    private function deliveryMethod(array $shippingLine)
    {
        $thirdPartyDelivered = 0;
        //check if the carrier product exist agains the shipping rule that is selected at checkout page
        $carrier_product = CarrierProduct::where('carrier_product_code', explode('#', $shippingLine['code'])[0])->first();
        //if it exists and its a parcel shops and has multiple parcel shop, then take the carrier and add _service_point i.e, (GLS_SERVICE_POINT)
        if ($carrier_product && $carrier_product->hasMultiParcelShop) {
            $shippingMethodId = explode('#', $shippingLine['code'])[2] ?? null;

            if ($shippingMethodId) {
                $shippingMethod = ShippingMethod::withTrashed()->find($shippingMethodId);
                $delivery_title = $shippingMethod->title ?? $carrier_product->carrier->carrier_id . '_SERVICE_POINT';
            } else {
                $delivery_title = $carrier_product->carrier->carrier_id . '_SERVICE_POINT';
            }
        } elseif ($carrier_product) {
            // if the carrier product does not exist then take whatever is coming in request from shopify i.e,(Standard)
            $delivery_title = $shippingLine['title'];
        } else {
            $delivery_title = $shippingLine['title'];
            $thirdPartyDelivered = 1;
        }

        // check if the delivery method is already created then dont create a new and return the old one
        $model = DeliveryMethod::where([['integration_code', '=', $this->integrationCode], ['name', '=', $delivery_title]])->where('is_active', 1)->first();
        // if the delivery method is not created then create a new delivery template
        if (!$model) {
            $model = DeliveryMethod::create([
                'delivery_method_code' => generateHash(),
                'name' => $delivery_title,
                'integration_code' => $this->integrationCode,
                'third_party_delivered' => $thirdPartyDelivered
            ]);
        }
        return $model;
    }

    function handleRefundStatusFromShopify()
    {
        if (isset($this->order['refunds']) && is_array($this->order['refunds'])) {

            foreach ($this->order['refunds'] as $refund) {

                if (isset($refund['transactions']) && count($refund['transactions']) > 0) {
                    foreach ($refund['transactions'] as $transaction) {

                        $orderRefund = OrderRefund::where('order_id', $transaction['order_id'])
                            ->where('transaction_id',  $transaction['id'])
                            ->where('refund_id', $refund['id'])
                            ->where('status', 'pending')
                            ->first();

                        if ($orderRefund) {
                            $orderRefund->update(['status' => $transaction['status']]);
                            SalesInvoice::where('order_refund_code', $orderRefund->order_refund_code)
                                ->update(['status' => $transaction['status']]);

                            $oldEvent = Event::where('event_id', $orderRefund->order_refund_code)->first();
                            $author = isset($oldEvent) ? $oldEvent->author : 'Shopify Admin';
                            $amount = $this->removeTrailingZeros($transaction['amount']);

                            $message = generateRefundMessage($author, ($transaction['status'] ?? 'TranSactionStatus'), $amount, $transaction['currency']);

                            if ($transaction['status'] == 'success') {
                                event(
                                    new SaveOrderEvent(
                                        $this->model['order_code'],
                                        'Order',
                                        'fulfillment_refunded',
                                        $message,
                                        $author . ' fulfillment_refunded ',
                                    )
                                );
                            }
                        }
                    }
                }
            }
        }
    }

    function removeTrailingZeros($number)
    {
        $numberString = rtrim(strval($number), '0');
        if (substr($numberString, -1) === '.') {
            $numberString = substr($numberString, 0, -1);
        }

        return $numberString;
    }

    /**
     * Creates an order record
     *
     * @return bool
     */
    private function create(): bool
    {
        $this->isNewOrder = true;
        $this->model = new Order();
        $this->model->order_code = generateHash();
        $this->model->integration_code = $this->integrationCode;
        $this->model->company_code = $this->companyCode;

        $this->populateModel();
        return $this->model->save();
    }

    public function saveEvents(): void
    {
        $events = $this->order['events'] ?? null;

        if ($events == null) {
            return;
        }

        $this->model->events()->delete();
        $orderCode = $this->model->order_code;

        foreach ($events as $event) {
            Event::create(
                [
                    'event_code' => generateHash(),
                    'order_code' => $orderCode,
                    'event_id' => $event['id'],
                    'subject_type' => $event['subject_type'],
                    'verb' => $event['verb'],
                    'author' => $event['author'],
                    'message' => $event['message'],
                    'body' => $event['body'],
                    'description' => $event['description'],
                    'arguments' => $event['arguments'],
                    'created_at' => $event['created_at'] ?? Carbon::now(),
                    'updated_at' => date('Y-m-d H:i:s')
                ]
            );
        }
    }

    public function saveLineItems(): void
    {
        $lineItems = $this->order['line_items'] ?? null;
        if ($lineItems == null) {
            return;
        }

        $orderCode = (int) $this->model->order_code;

        foreach ($lineItems as $item) {
            $model = LineItem::where([['order_code', '=', $orderCode], ['line_item_id', '=', $item['id']]])->first();
            $productCode = Product::where([['integration_code', '=', $this->integrationCode], ['product_id', '=', $item['product_id']]])->pluck('product_code')->first();

            $platformProduct = null;
            $variant = null;

            if (isset($item['product_id'])) {
                $integration = Integration::where('integration_code', $this->integrationCode)->with('platform')->whereNull('deleted_at')->first();

                if ($integration) {

                    $platformService = app(EcommerceServiceInterface::class, ['platform' => $integration->platform->name]);
                    $platformProduct = $platformService->getSingleProduct($integration->myshopify_domain,  $integration->accesstoken, $item['product_id']);

                    if ($platformProduct) {
                        //converting to array
                        $platformProduct = json_decode(json_encode($platformProduct), true);

                        // iterating over the platformProduct variant to match it with the line item
                        $variant = array_filter($platformProduct['variants'], function ($variant) use ($item) {
                            return $variant['id'] == $item['variant_id'];
                        });

                        // remove the first element from variant array and return that removed element
                        $variant = array_shift($variant);
                        //if variant exists then add image to that variant
                        if ($variant) {

                            //images exists in shopify product, iterate over them
                            if (isset($platformProduct['images'])) {
                                foreach ($platformProduct['images'] as $image) {

                                    //if the variant id and image id matches then add to variant array (returned by array_shift)
                                    if ($variant['image'] && $variant['image']['id'] == $image['id']) {
                                        $variant['variant_image'] = $image['src'];
                                        break; // stop looping once the image is found
                                    } else {
                                        //if the image does not exist for variant take the defualt image of that product
                                        $variant['variant_image'] = $platformProduct['image']['src'] ?? null;
                                    }
                                }
                            }

                            //if the product code does not exist(means product is not inserted in our db)
                            if (isset($platformProduct['id'])) {
                                $this->saveNewProduct($this->integrationCode, $this->companyCode, $platformProduct);
                                $productCode = Product::where([['integration_code', '=', $this->integrationCode], ['product_id', '=', $item['product_id']]])->pluck('product_code')->first();
                            }
                        }
                    }
                }
            }
            if (isset($item['discount_allocations']) && is_array($item['discount_allocations'])) {
                $item['discount_allocations'] = array_map(function ($discountAllocation) use ($item) {
                    $index = $discountAllocation['discount_application_index'];
                    if (isset($this->order['discount_applications'][$index])) {
                        $discountAllocation['discount_application'] = $this->order['discount_applications'][$index];
                        $discountAllocation['discount_application']['single_item_discount'] =
                            round($discountAllocation['amount_set']['presentment_money']['amount'] / $item['quantity'], 2);
                    }
                    return $discountAllocation;
                }, $item['discount_allocations']);

                $totalDiscountSet = array_map(function ($discountAllocation) {
                    return $discountAllocation['amount_set'] ?? 0;
                }, $item['discount_allocations']);
            }


            //inserting each line item in our db and alson inserted its variant along with it.
            $lineItemData = [
                'order_code' => $orderCode,
                'line_item_id' => $item['id'],
                'sku' => $item['sku'],
                'fulfillable_quantity' => $item['fulfillable_quantity'],
                'fulfillment_service' => $item['fulfillment_service'],
                'fulfillment_status' => $item['fulfillment_status'],
                'gift_card' => $item['gift_card'],
                'grams' => $item['grams'] ?? null,
                'name' => $item['name'],
                'price' => $item['price'] ?? null,
                'price_set' => $item['price_set'] ?? null,
                'product_id' => $item['product_id'],
                'product_code' => $productCode ?? null,
                'quantity' => $item['quantity'],
                'requires_shipping' => $item['requires_shipping'],
                'taxable' => $item['taxable'],
                'title' => $item['title'],
                'total_discount' => $totalDiscountAmount ?? 0,
                'total_discount_set' => $totalDiscountSet ?? [],
                'variant' => $variant ?? null,
                'variant_id' => $item['variant_id'],
                'variant_title' => $item['variant_title'],
                'tax_lines' => $item['tax_lines'],
                'duties' => $item['duties'],
                'discount_allocations' => $item['discount_allocations'] ?? [],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            if (!$model) {
                LineItem::create(["line_item_code" => generateHash()] + $lineItemData);
            } else {
                $model->update($lineItemData);
            }
        }
    }

    function saveNewProduct($integration_code, $company_code, $product)
    {
        return (new ProductService($integration_code, $company_code, $product))->save();
    }

    public function saveOrderRisks(): void
    {
        $risks = $this->order['risks'] ?? null;

        if ($risks == null || count($risks) == 0) {
            return;
        }

        $this->model->risks()->delete();
        $orderCode = $this->model->order_code;

        foreach ($risks as $item) {
            OrderRisk::create(
                [
                    'order_risk_code' => generateHash(),
                    'order_code' => $orderCode,
                    'order_risk_id' => $item['id'],
                    'checkout_id' => $item['checkout_id'],
                    'source' => $item['source'],
                    'score' => $item['score'],
                    'recommendation' => $item['recommendation'],
                    'display' => $item['display'],
                    'cause_cancel' => $item['cause_cancel'],
                    'message' => $item['message'],
                    'merchant_message' => $item['merchant_message'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]
            );
        }
    }

    /**
     * Saves order transactions from Shopify to the database
     */
    public function saveOrderTransactions(): void
    {
        try {
            if (!isset($this->model->integration) || !isset($this->model->order_id)) {
                Log::warning('Missing required model properties for saving transactions');
                return;
            }

            $transactions = $this->fetchShopifyTransactions();
            if (empty($transactions)) {
                return;
            }

            $orderCode = $this->model->order_code;

            // Process all transactions directly since updateOrCreate handles both cases
            foreach ($transactions as $item) {
                if (!$this->isValidTransaction($item)) {
                    Log::warning('Invalid transaction data', ['transaction' => $item]);
                    continue;
                }

                try {
                    $this->saveTransaction($item, $orderCode);
                } catch (\Exception $e) {
                    Log::error('Error saving individual transaction: ' . $e->getMessage(), [
                        'transaction_id' => $item['id'] ?? null
                    ]);
                }
            }
            $authTransaction = collect($transactions)->first(function ($t) {
                return $t['kind'] === 'AUTHORIZATION' && $t['status'] === 'SUCCESS';
            });

            if ($authTransaction) {
                $expiresAtRaw = $authTransaction['authorization_expires_at'] ?? null;
                $expiresAt = $expiresAtRaw ? Carbon::parse($expiresAtRaw)->format('Y-m-d H:i:s') : null;
                $status = $this->checkExpiry($authTransaction) ?? 'unknown';

                $this->model->update([
                    'authorization_expires_at' => $expiresAt,
                    'authorization_status' => $status,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error saving order transactions: ' . $e->getMessage(), [
                'order_id' => $this->model->order_id ?? null,
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Fetches transactions from Shopify API
     */
    private function fetchShopifyTransactions(): array
    {
        $shopifyService = app(EcommerceServiceInterface::class, ['platform' => "shopify"]);
        $response = $shopifyService->getOrderTransactions(
            $this->model->integration->myshopify_domain,
            $this->model->integration->accesstoken,
            $this->model->order_id
        );

        return $response['data']['order']['transactions'] ?? [];
    }

    /*
    * Validates transaction data
    */
    private function isValidTransaction(array $transaction): bool
    {
        return isset($transaction['id'])
            && isset($transaction['amount_set']['presentment_money']['amount'])
            && isset($transaction['amount_set']['presentment_money']['currency_code']);
    }

    /**
     * Saves individual transaction
     *
     * @param array $item
     * @param string $orderCode
     * @return void
     */
    private function saveTransaction(array $item, string $orderCode): void
    {
        $existingTransaction = OrderTransaction::where([
            'order_transaction_id' => $item['id'],
            'order_code' => $orderCode,
        ])->first();

        // Default to null unless we get meaningful values
        $authorizationStatus = null;
        $authorizationExpiresAt = null;

        if ($item['kind'] === 'AUTHORIZATION') {
            $authorizationStatus = $this->checkExpiry($item);
            $expiresAtRaw = $authTransaction['authorization_expires_at'] ?? null;
            $authorizationExpiresAt = $expiresAtRaw ? Carbon::parse($expiresAtRaw)->format('Y-m-d H:i:s') : null;
        }

        $transactionData = [
            'order_transaction_code' => $existingTransaction?->order_transaction_code ?? generateHash(),
            'kind' => $item['kind'],
            'gateway' => $item['gateway'],
            'status' => $item['status'],
            'message' => $item['message'] ?? null,
            'authorization_status' => $authorizationStatus,
            'authorization_expires_at' => $authorizationExpiresAt,
            'authorization' => $item['authorization'] ?? null,
            'test' => $item['test'],
            'receipt' => $item['receipt'] ?? null,
            'amount' => $item['amount_set']['presentment_money']['amount'],
            'currency' => $item['amount_set']['presentment_money']['currency_code'],
            'payment_details' => $item['payment_details'] ?? null,
            'source_name' => $item['source_name'] ?? null,
            'created_at' => $item['created_at'] ?? Carbon::now(),
            'updated_at' => now()
        ];

        OrderTransaction::updateOrCreate(
            [
                'order_transaction_id' => $item['id'],
                'order_code' => $orderCode,
            ],
            $transactionData
        );
    }


    public function checkExpiry($transaction)
    {
        if ($transaction['kind'] === 'AUTHORIZATION') {
            $expiresAt = $transaction['authorization_expires_at'];

            if ($expiresAt) {
                $expiresAt = Carbon::parse($expiresAt);
                $now = Carbon::now();

                if ($now->greaterThan($expiresAt)) {
                    $authorizationStatus = 'expired';
                } elseif ($now->diffInHours($expiresAt) <= 48) {
                    $authorizationStatus = 'expiring';
                } else {
                    $authorizationStatus = 'authorized';
                }
            } else {
                $authorizationStatus = 'unknown';
            }
            return $authorizationStatus;
        }
    }

    private function assignAutoTemplate()
    {
        $order = Order::where('order_code', $this->model->order_code)->first();
        $line_items = LineItem::where('order_code', $this->model->order_code)->get();

        $decided_template = false;
        if (isset($order->order_code)) {
            $order_weight = 0.00;
            //calculating total weight of the order from line items related to that order
            foreach ($line_items as $line_item) {
                $product = Product::where('product_id', $line_item->product_id)->first();
                if (!$product) {
                    continue;
                }

                $lineItemWeight = $line_item->quantity * (isset($line_item->grams) ? convertGramToKg($line_item->grams) : $product->weight);
                $order_weight += $lineItemWeight;
            }

            // if stored weight and coming weight are matched dont update the template
            if (($this->isNewOrder) || ($this->isWeightChanged)) {

                if (isset(($order->shipping_address ?? $order->billing_address)['country_code']) && isset($order->shipping_lines[0])) {

                    $shipping_country = Country::where('iso', ($order->shipping_address ?? $order->billing_address)['country_code'])->first();
                    $carrier_product = CarrierProduct::where('carrier_product_code', explode('#', $order->shipping_lines[0]['code'])[0])->first();
                    $company = null;

                    //if carrier product exists, then shipvagoo shipping carrier is used, now getting the template of shipvagoo merchant agreement
                    if ($shipping_country && $carrier_product) {

                        //getting templates of the company(merchant)
                        $templates = Template::where('company_code', $this->model->company_code)->where('is_active', 1)->get();
                        foreach ($templates as $template) {
                            $decided_template = $this->compareTemplateAndOrder($shipping_country, $template, $carrier_product->carrier_product_code, $order_weight);
                            if ($decided_template) break;
                        }

                        //if template is decided, then store the delivery template code in both delivery and return delivery template of the order
                        if ($decided_template) {
                            $status = $order->template_status !== "created" ? "existing" : "created";
                            $this->setOrderDeliveryTemplate($order, $decided_template->template_code, $decided_template->template_code, $status);
                            return true;
                        }

                        // if the templat does not exist then create a new one and assign it to order
                        $company = Company::where('company_code', $this->companyCode)->first();

                        if ($company && $company->merchant) {
                            //creating new shipping template

                            $template = $this->createNewShippingTemplate($company->merchant, $order_weight, $shipping_country, $carrier_product);

                            //when the template is created we will assign it to the order (both delivery and return delivery template), it will be considered a freshly created template
                            if ($template && $template['template_code']) {
                                $this->setOrderDeliveryTemplate($order, $template['template_code'], $template['template_code'], "created");
                                return;
                            }
                        }
                    } else {
                        // Shipping carrier selected at shopify checkout is not from shipvagoo, i,e, Standard, Express etc (High Level)

                        //template assigned to shipping rule (high level assignment)
                        $template = $order->deliveryMethod->deliveryTemplate;
                        //if template at hight level assignment exists
                        if ($template) {
                            // comparing receiver_country & weight of hight level assigned template with the order, if matches then assign it to the order
                            $decided_template = $this->compareTemplateAndOrder($shipping_country, $template, $template->carrier_product, $order_weight);

                            if ($decided_template) {
                                $this->setOrderDeliveryTemplate($order, $decided_template->template_code, $decided_template->template_code, "manual");
                                return;
                            }

                            // if high level assigned template does not matched fetch templates of same carrier product, company, receiver country and is_active
                            $templates = Template::where([
                                'carrier_product_code' => $template->carrier_product_code,
                                'company_code' => $this->model->company_code,
                                'is_active' => 1,
                                'receiver_country_code' => $shipping_country->country_code,
                            ])->get();

                            $highestWeightTemplate = $carrier_product = null;
                            foreach ($templates as $template) {

                                // if any template matches the order weight, then assign it to the order
                                $decided_template = $this->compareTemplateAndOrder($shipping_country, $template, $template->carrier_product_code, $order_weight);
                                if ($decided_template) break;

                                // finding the template with the highest maximum weight
                                if ($highestWeightTemplate === null || (isset($template->parcels[0]['max_weight']) && $template->parcels[0]['max_weight'] > $highestWeightTemplate->parcels[0]['max_weight'])) {
                                    $highestWeightTemplate = $template;
                                }
                            }

                            //if template is decided, then store the delivery template code in both delivery and return delivery template of the order
                            if ($decided_template) {
                                $this->setOrderDeliveryTemplate($order, $decided_template->template_code, $decided_template->template_code, "existing");
                                return true;
                            } else {
                                if (!isset($company)) {
                                    $company = Company::where('company_code', $this->companyCode)->with('merchant')->first();
                                }

                                if (!isset($carrier_product)) {
                                    $carrier_product = CarrierProduct::where('carrier_product_code', $template->carrier_product_code)->first();
                                }

                                //if company has merchant
                                if ($company && $company->merchant && $carrier_product) {
                                    //creating new shipping template
                                    $template = $this->createNewShippingTemplate($company->merchant, $order_weight, $shipping_country, $carrier_product);

                                    // when template is created we will assign it to order, it will be considered a freshly created template
                                    if ($template && $template['template_code']) {
                                        $this->setOrderDeliveryTemplate($order, $template['template_code'], $template['template_code'], "created");
                                        return;
                                    }
                                }
                            }

                            /**
                             * if high level assigned template or other templates of same carrier product as high level assigned template does not cover the weight and shipping country
                             * then assign any template with the highest weight of the carrier product as high level assigned template
                             */
                            if ($highestWeightTemplate) {
                                $this->setOrderDeliveryTemplate($order, $highestWeightTemplate->template_code, $highestWeightTemplate->template_code, "manual");
                                return true;
                            }
                        }

                        // if the template is not found, nor created and not even assigned of the heighest weight of the carrier product, then assign the template created inside the deliveryMethod() function
                        $this->setOrderDeliveryTemplate($order, $order->deliveryMethod->delivery_template_code, $order->deliveryMethod->return_template_code, "manual");
                    }
                }
            }
        }
    }

    public function createNewShippingTemplate($merchant, $order_weight, $shipping_country, $carrier_product)
    {
        //iterating over the agreements of the merchant
        $additional_services_repository = app(\Main\Modules\Shipment\Repositories\ASChargeRepository::class);

        // find check if the exact match of weight_class exists
        foreach ($merchant->agreement->pricing as $price) {
            $ubsend_price = $price->ShipvagooPricing->ubsend_price;
            if (
                $order_weight >= $ubsend_price['weight_class_from'] &&
                $order_weight <= $ubsend_price['weight_class_to'] &&
                $shipping_country->default_name == $ubsend_price['to_country'] &&
                $carrier_product->product_id == $ubsend_price['monty_product']
            ) {
                return $this->createTemplateFromUbsendPrice($ubsend_price, $shipping_country, $carrier_product, $additional_services_repository);
            }
        }

        // if no exact match, find pricing options where only weight_class_to is the highest among other failed pricing, chose the one with the highest weight_class_to
        $fallback_options = [];
        foreach ($merchant->agreement->pricing as $price) {
            $ubsend_price = $price->ShipvagooPricing->ubsend_price;
            if (
                $order_weight >= $ubsend_price['weight_class_from'] &&
                $order_weight > $ubsend_price['weight_class_to'] &&
                $shipping_country->default_name == $ubsend_price['to_country'] &&
                $carrier_product->product_id == $ubsend_price['monty_product']
            ) {
                // adding all of the ubsend_price in an array where we will sort and get the first which is the most highest in descending order
                $fallback_options[] = $ubsend_price;
            }
        }

        if (!empty($fallback_options)) {

            // Sort fallback options by weight_class_to in descending order
            usort($fallback_options, function ($a, $b) {
                return $b['weight_class_to'] <=> $a['weight_class_to'];
            });

            // use the first option (highest weight_class_to)
            $best_match = $fallback_options[0];

            // checking if the $fallback_options[0] exists in db then assign it otherwise create it
            $isTemplateExist = Template::where([
                'company_code' => $this->companyCode,
                'carrier_product_code' => $carrier_product->carrier_product_code,
                'sender_country_code' => **********,
                'receiver_country_code' => $shipping_country->country_code,
                'ubsend_client_id' => $best_match->ubsendAccount->client_id
            ])
                ->whereJsonContains('parcels', [
                    'min_weight' => $best_match['weight_class_from'],
                    'max_weight' => $best_match['weight_class_to']
                ])->first();

            if ($isTemplateExist) {
                $this->setOrderDeliveryTemplate($this->model, $isTemplateExist->template_code, $isTemplateExist->template_code, "existing");
                return;
            }

            return $this->createTemplateFromUbsendPrice($best_match, $shipping_country, $carrier_product, $additional_services_repository);
        }

        return null;
    }

    // Helper method to create template from ubsend price
    private function createTemplateFromUbsendPrice($ubsend_price, $shipping_country, $carrier_product, $additional_services_repository)
    {
        $shipping_template = [
            'name' => 'DK - ' . $shipping_country->iso . ' - ' . $carrier_product->carrier->name . ' - ' . $carrier_product->name . ' - ' . number_format((float)$ubsend_price['weight_class_from'], 3) . '-' . number_format((float)$ubsend_price['weight_class_to'], 3) . ' KG',
            'carrier_product_code' => $carrier_product->carrier_product_code,
            'sender_country_code' => **********,
            'receiver_country_code' => $shipping_country->country_code,
            'company_code' => $this->companyCode,
            'is_default' => 0,
            'ubsend_client_id' => $ubsend_price->ubsendAccount->client_id,
            'parcels' => [
                [
                    "count" => 1,
                    "width" => 1,
                    "height" => 1,
                    "length" => 1,
                    "min_weight" => $ubsend_price['weight_class_from'],
                    "max_weight" => $ubsend_price['weight_class_to'],
                    "weight" => $ubsend_price['weight_class_to'],
                    "description" => "Parcel " . $ubsend_price['weight_class_from'] . " - " . $ubsend_price['weight_class_to'] . " KG"
                ]
            ],
            'additional_services' => []
        ];

        $records = $additional_services_repository->carrierProductSpecificAdditionalServicesCollection(
            **********,
            (int)$shipping_country->country_code,
            (int)$carrier_product->carrier_product_code
        );

        foreach ($records->toArray() as $record) {
            if ($record['additional_service_code'] == **********) {
                $shipping_template['additional_services'][] = $record['as_charge_code'];
                break;
            }
        }

        return $this->templateService->storeTemplate($shipping_template);
    }

    public function setOrderDeliveryTemplate($order, $delivery_template_code, $return_template_code, $status)
    {
        $order->delivery_template_code = $delivery_template_code;
        $order->return_template_code = $return_template_code;
        $order->template_status = $status;
        $order->save();
    }

    public function compareTemplateAndOrder($shipping_country, $template, $carrier_product_code, $order_weight)
    {
        /*
        if shipping country code is equal to template receiver code,
        the carrier product code of the carrier(the user selected on checkout page of shopify) is equalt to template carrier product code,
        getting weight from the template description
        */
        if (
            $shipping_country->country_code == $template->receiver_country_code &&
            $carrier_product_code == $template->carrier_product_code &&
            preg_match("/([\d\.]+)\s-\s([\d\.]+)\sKG/", $template->parcels[0]['description'], $matches)
        ) {

            //getting minimum and maximum weight of the parcel from matches fround from template description
            $lowerLimit = floatval($matches[1]);
            $upperLimit = floatval($matches[2]);

            //if order total weight is in range of template then break the further loop of templates
            if ($order_weight >= $lowerLimit && $order_weight <= $upperLimit) {
                return $template;
            } else {
                return null;
            }
        }
    }

    public function createPrintJobForPickListOfOrder(): void
    {
        /** @var PrinterRepository $printerRepository */
        $printerRepository = app(PrinterRepository::class);

        /** @var OrderRepository $orderRepository */
        $orderRepository = app(OrderRepository::class);

        $defaultPrinter = $printerRepository->getDefaultPrinterForPickLists($this->companyCode);

        /**
         * If there is no default printer for pick lists, then we don't need to create a print job.
         * But, we still need to update is_printed_pick_list to 1 in the orders table.
         * Because we don't want to create a print job for all the orders at once in the future as
         * soon as a printer is assigned for pick lists.
         *
         * TL;DR: If we don't set this, and if a printer assigned for pick lists is added later,
         * IT WILL CREATE A PRINT JOB FOR ALL THE ORDERS.
         */
        if (!$defaultPrinter) {
            $orderRepository->markPickListOfOrderIsPrinted($this->model['order_code']);
        }

        if ($defaultPrinter && $defaultPrinter->is_auto_print_pick_list == 1) {
            /** @var PrintJobService $printJobService */
            $printJobService = app(PrintJobService::class);

            /** @var PrintJobRequest $printJobCreateRequest */
            $printJobCreateRequest = app(PrintJobRequest::class);

            /** @var ServicesOrderService $orderService */
            $orderService = app(ServicesOrderService::class);
            $orderServiceResponse = $orderService->createPickList((int)$this->model['order_code']);
            $orderServiceResponse = $orderServiceResponse->toArray();

            $printJobCreateRequest->override([
                'print_job_code' => generateHash(),
                'company_code' => $this->companyCode,
                'printer_app_code' => $defaultPrinter->printer_app_code,
                'printer' => $defaultPrinter->linked_printer,
                'format' => $defaultPrinter->format,
                'document_base64' => $orderServiceResponse['file'],
                'reference' => ("Picking list # " . $this->model['name']) ?? ("Picking list # " . $this->model['order_number'])
            ]);

            $print_job = $printJobService->createPrintJob($printJobCreateRequest, $this->companyCode);
            $orderRepository->markPickListOfOrderIsPrinted($this->model['order_code']);
        }
    }

    public function edit()
    {
        $order = $this->order['order_edit'];
        $lineItems = $order['line_items'];
        $message = null;

        // code for items are added
        if (count($lineItems['additions']) > 0) {
            foreach ($lineItems['additions'] as $item) {
                $lineItem = $this->lineItemRepository->getLineItemsByLineItemID($item['id']);
                if ($lineItem) {
                    $message = "Shopify Admin addded " . $item['delta'] . " x " . ($lineItem->name ?? $lineItem->product->title ?? "");
                    if ($message) {
                        $order = Order::where('order_id', $order['order_id'])->first();
                        $this->createOrderEvent($order->order_code, "Order", "order_edited", $message, "Shopify Admin");
                    }
                }
            }
        }
        if (count($lineItems['removals']) > 0) {
            //code for items are removed
            foreach ($lineItems['removals'] as $item) {
                $lineItem = $this->lineItemRepository->getLineItemsByLineItemID($item['id']);
                if ($lineItem && ($lineItem->fulfillable_quantity + $item['delta'] <= $lineItem->quantity)) {
                    $preQty = $lineItem->returned_quantity;
                    $totalReturnedQty = $preQty + $item['delta'];
                    $lineItem->update(['returned_quantity' => $totalReturnedQty]);
                    $message = "Shopify Admin removed " . $item['delta'] . " x " . ($lineItem->name ?? $lineItem->product->title ?? "");
                    if ($message) {
                        $order = Order::where('order_id', $order['order_id'])->first();
                        $this->createOrderEvent($order->order_code, "Order", "order_edited", $message, "Shopify Admin");
                    }
                }
            }
        }
    }
}
