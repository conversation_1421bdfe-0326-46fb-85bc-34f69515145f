<?php

namespace Main\Modules\Ecommerce\Woocommerce;

use Illuminate\Support\Facades\Http;

class WoocommerceRepository
{

    protected $baseUrl;
    protected $apiKey;

    public function __construct()
    {
        $this->baseUrl = config('ecommerce.woocommerceRepository.base_url');
        $this->apiKey = config('ecommerce.woocommerceRepository.api_key');
    }

    public function fetchOrders(): array
    {
        $response = Http::withHeaders([
            'X-Shopify-Access-Token' => $this->apiKey,
        ])->get("{$this->baseUrl}/admin/api/2024-01/orders.json");

        return $response->json('orders') ?? [];
    }

    public function getOrderDetails($orderId): array
    {
        $response = Http::withHeaders([
            'X-Shopify-Access-Token' => $this->apiKey,
        ])->get("{$this->baseUrl}/admin/api/2024-01/orders/{$orderId}.json");

        return $response->json('order') ?? [];
    }
}
