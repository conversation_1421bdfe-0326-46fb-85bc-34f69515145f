<?php

namespace Main\Modules\Company\Controllers;

use Core\Controllers\AbstractController;
use Illuminate\Http\JsonResponse;
use Main\Modules\Company\Requests\PersonalizedMessageRequest;
use Main\Modules\Company\Services\PersonalizedMessageService;

class PersonalizedMessageController extends AbstractController
{
    /**
     * @param PersonalizedMessageService $service
     * @param PersonalizedMessageRequest $request
     */
    public function __construct(PersonalizedMessageService $service, PersonalizedMessageRequest $request)
    {
        $this->domainService = $service;
        $this->domainRequest = $request;
    }
}
