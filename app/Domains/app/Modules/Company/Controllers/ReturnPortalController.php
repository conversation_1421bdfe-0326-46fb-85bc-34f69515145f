<?php

namespace Main\Modules\Company\Controllers;

use Core\Controllers\AbstractController;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Main\Modules\Company\Requests\ReturnPortalRequest;
use Main\Modules\Company\Services\ReturnPortalService;

class ReturnPortalController extends AbstractController
{
    /**
     * @param ReturnPortalService $service
     * @param ReturnPortalRequest $request
     */
    public function __construct(ReturnPortalService $service, ReturnPortalRequest $request)
    {
        $this->domainService = $service;
        $this->domainRequest = $request;
    }

    public function updateReceiver(int $returnPortalCode, Request $request): JsonResponse
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->updateReceiver($returnPortalCode, $this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }

    public function updateText(int $returnPortalCode, Request $request): JsonResponse
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->updateText($returnPortalCode, $this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }
}
