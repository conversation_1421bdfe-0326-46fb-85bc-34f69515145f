<?php
namespace Main\Modules\Company\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Core\Controllers\AbstractController;
use Main\Modules\Company\Requests\ReturnReasonRequest;
use Main\Modules\Company\Services\ReturnReasonService;

class ReturnReasonController extends AbstractController
{
    /**
     * @param ReturnReasonService $service
     * @param ReturnReasonRequest $request
     */
    public function __construct(
        ReturnReasonService $service,
        ReturnReasonRequest $request
    ) {
        $this->domainService = $service;
        $this->domainRequest = $request;
    }

    public function returnPortalReasons(int $returnPortalCode, Request $request): JsonResponse
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->returnPortalReasons($returnPortalCode, $this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }

    public function createReturnReason(int $returnPortalCode, Request $request): JsonResponse
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->createReturnReason($returnPortalCode, $this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }

    public function returnReason(int $returnPortalCode, int $returnReasonCode, Request $request): JsonResponse
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->returnReason($returnReasonCode, $this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }

    public function updateReturnReason(int $returnPortalCode, int $returnReasonCode, Request $request): JsonResponse
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->updateReturnReason($returnReasonCode, $this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }

    public function deleteReturnReason(int $returnPortalCode, int $returnReasonCode, Request $request): JsonResponse
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->deleteReturnReason($returnReasonCode, $this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }
}
