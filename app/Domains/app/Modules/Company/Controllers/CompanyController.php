<?php

namespace Main\Modules\Company\Controllers;

use App\Models\MerchantAgreement;
use App\Models\User;
use Core\Controllers\AbstractController;

use Main\Modules\Company\Requests\CompanyRequest;
use Main\Modules\Company\Services\CompanyService;

use Illuminate\Http\Request;
use Main\Modules\Platforms\Entities\Integration;
use Main\Modules\Platforms\Models\Integration as ModelsIntegration;
use Main\Modules\Shipment\Models\Carrier;
use Main\Modules\User\Entities\User as EntitiesUser;
use Main\Modules\User\Models\User as ModelsUser;

/**
 * @property CompanyService $domainService
 * @property CompanyRequest $domainRequest
 */
class CompanyController extends AbstractController
{
    public function __construct(CompanyService $service, CompanyRequest $request)
    {
        $this->domainService = $service;
        $this->domainRequest = $request;
    }

    /**
     * @param Request $request
     *
     * @return [type]
     */
    public function register(Request $request)
    {
        $this->domainRequest->set($request->all());
        $companyResponse = $this->domainService->register($this->domainRequest);
        return response()->json($companyResponse->getData(), $companyResponse->code());
    }

    public function getCurrentCompany(Request $request)
    {
        $this->domainRequest->set($request->all());
        $companyResponse = $this->domainService->getCurrentCompany($this->domainRequest);
        return response()->json($companyResponse->getData()['entities'][0], $companyResponse->code());
    }

    public function getShippingMethods(Request $request)
    {
        return '{ "rates": [ { "service_name": "canadapost-overnight", "service_code": "ON", "total_price": "1395", "description": "This is the fastest option by far.", "currency": "CAD", "min_delivery_date": "2013-04-12 14:48:45 -0400", "max_delivery_date": "2013-04-12 14:48:45 -0400" }, { "service_name": "fedex-2dayground", "service_code": "2D", "total_price": "2934", "currency": "USD", "min_delivery_date": "2013-04-12 14:48:45 -0400", "max_delivery_date": "2013-04-12 14:48:45 -0400" }, { "service_name": "fedex-priorityovernight", "service_code": "1D", "total_price": "3587", "currency": "USD", "min_delivery_date": "2013-04-12 14:48:45 -0400", "max_delivery_date": "2013-04-12 14:48:45 -0400" } ] }';
    }

    public function agreement(ModelsUser $user_code)
    {
        $agreement = $user_code->load('merchantAgreement.pricing.priceGroup', 'merchantAgreement.pricing.ShipvagooPricing.ubsend_price');

        return response()->json($agreement, 200);
    }

    public function userAgreementCarriers(ModelsUser $user_code)
    {
        $agreement = MerchantAgreement::where('merchant_id', auth()->id())->with('pricing.priceGroup', 'pricing.ShipvagooPricing.ubsend_price.ubsendAccount')->first();

        $carriers = [];

        foreach ($agreement->pricing as $pricing) {

            $carriers[]  = $pricing->ShipvagooPricing->ubsend_price->carrier;
        }


        $carriers = array_unique($carriers);

        $finalCarrier = array();

        foreach ($carriers as $carrier) {
            $carrierModel = Carrier::where('name', $carrier)->first();

            $finalCarrier[] = [
                'name'  =>  $carrier,
                'url'   =>  $carrierModel->icon
            ];
        }

        $shops = ModelsIntegration::select('name', 'integration_code')->where('company_code', auth()->user()->company_code)->get()->toArray();


        $response = [
            'carriers'  =>  $finalCarrier,
            'shops'     =>  $shops
        ];

        return response()->json($response, 200);
    }
}
