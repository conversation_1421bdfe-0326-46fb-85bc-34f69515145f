<?php
namespace Main\Modules\Company\Controllers;

use Core\Controllers\AbstractController;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Main\Modules\Company\Requests\MethodRequest;
use Main\Modules\Company\Services\MethodService;

class MethodController extends AbstractController
{
    /**
     * @param MethodService $service
     * @param MethodRequest $request
     */
    public function __construct(
        MethodService $service,
        MethodRequest $request
    ) {
        $this->domainService = $service;
        $this->domainRequest = $request;
    }

    public function methods(int $returnPortalCode, Request $request): JsonResponse
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->methods($returnPortalCode, $this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }

    public function createMethod(int $returnPortalCode, Request $request): JsonResponse
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->createMethod($returnPortalCode, $this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }

    public function method(int $returnPortalCode, int $methodCode, Request $request): JsonResponse
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->Method($methodCode, $this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }

    public function updateMethod(int $returnPortalCode, int $methodCode, Request $request): JsonResponse
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->updateMethod($returnPortalCode, $methodCode, $this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }

    public function deleteMethod(int $returnPortalCode, int $methodCode, Request $request): JsonResponse
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->deleteMethod($methodCode, $this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }
}
