<?php
namespace Main\Modules\Company\Controllers;

use Core\Controllers\AbstractController;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Main\Modules\Company\Requests\ResolutionRequest;
use Main\Modules\Company\Services\ResolutionService;

class ResolutionController extends AbstractController
{
    /**
     * @param ResolutionService $service
     * @param ResolutionRequest $request
     */
    public function __construct(
        ResolutionService $service,
        ResolutionRequest $request
    ) {
        $this->domainService = $service;
        $this->domainRequest = $request;
    }

    public function resolutions(int $returnPortalCode, Request $request): JsonResponse
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->returnPortalReasons($returnPortalCode, $this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }

    public function createResolution(int $returnPortalCode, Request $request): JsonResponse
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->createResolution($returnPortalCode, $this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }

    public function resolution(int $returnPortalCode, int $resolutionCode, Request $request): JsonResponse
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->Resolution($resolutionCode, $this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }

    public function updateResolution(int $returnPortalCode, int $resolutionCode, Request $request): JsonResponse
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->updateResolution($resolutionCode, $this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }

    public function deleteResolution(int $returnPortalCode, int $resolutionCode, Request $request): JsonResponse
    {
        $this->domainRequest->set($request->all());
        $response = $this->domainService->deleteResolution($resolutionCode, $this->domainRequest);
        return response()->json($response->getData(), $response->code());
    }
}
