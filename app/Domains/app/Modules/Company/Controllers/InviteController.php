<?php
namespace Main\Modules\Company\Controllers;

use Core\Controllers\AbstractController;

use Main\Modules\Company\Requests\InviteRequest;
use Main\Modules\Company\Services\InviteService;

class InviteController extends AbstractController
{
    public function __construct(InviteService $service, InviteRequest $request)
    {
        $this->domainService = $service;
        $this->domainRequest = $request;
    }

    public function invite(string $token)
    {
        $response = $this->domainService->getInvite($token);
        return response()->json($response->getData(), $response->code());
    }
}