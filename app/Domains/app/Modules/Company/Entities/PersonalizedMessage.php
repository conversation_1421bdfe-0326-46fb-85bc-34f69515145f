<?php
declare(strict_types=1);

namespace Main\Modules\Company\Entities;

use Core\Entities\AbstractEntity;

class PersonalizedMessage Extends AbstractEntity
{
    const TEMPLATE_TYPE = [
        'SMS'=>'SMS',
        'EMAIL'=>'EMAIL',
    ];

    const SEND_OPTION = [
        'IMMEDIATELY'=>'IMMEDIATELY',
        'SPECIFIC_TIME'=>'SPECIFIC_TIME',
    ];
    /**
     * messageCode
     *
     * @var
     */
    protected $messageCode;
    /**
     * type
     *
     * @var
     */
    protected $type;
    /**
     * messageHookCode
     *
     * @var
     */
    protected $messageHookCode;
    /**
     * isReceiverCountrySpecific
     *
     * @var
     */
    protected $isReceiverCountrySpecific;
    /**
     * receiverCountryCode
     *
     * @var
     */
    protected $receiverCountryCode;
    /**
     * companyCode
     *
     * @var
     */
    protected $companyCode;
    /**
     * isIntegrationSpecific
     *
     * @var
     */
    protected $isIntegrationSpecific;
    /**
     * integrationCode
     *
     * @var
     */
    protected $integrationCode;
    /**
     * sendOption
     *
     * @var
     */
    protected $sendOption;
    /**
     * sendTime
     *
     * @var
     */
    protected $sendTime;
    /**
     * bcc
     *
     * @var
     */
    protected $bcc;
    /**
     * subject
     *
     * @var
     */
    protected $subject;
    /**
     * messageContent
     *
     * @var
     */
    protected $messageContent;
    /**
     * messageHook
     *
     * @collection    \Main\Modules\Setting\Collections\MessageHookCollection
     * @factory       \Main\Modules\Setting\Factories\MessageHookFactory
     * @aggregateType entity
     * @var
     */
    protected $messageHook;
    /**
     * receiverCountry
     *
     * @collection    \Main\Modules\Location\Collections\CountryCollection
     * @factory       \Main\Modules\Location\Factories\CountryFactory
     * @aggregateType entity
     * @var
     */
    protected $receiverCountry;
    /**
     * integration
     *
     * @collection    \Main\Modules\Platforms\Collections\IntegrationCollection
     * @factory       \Main\Modules\Platforms\Factories\IntegrationFactory
     * @aggregateType entity
     * @var
     */
    protected $integration;
    /**
     * Company
     *
     * @collection    \Main\Modules\Company\Collections\CompanyCollection
     * @factory       \Main\Modules\Company\Factories\CompanyFactory
     * @aggregateType entity
     * @var
     */
    protected $company;
    /**
     * createdAt
     *
     * @var
     */
    protected $createdAt;
    /**
     * updatedAt
     *
     * @var
     */
    protected $updatedAt;
}
