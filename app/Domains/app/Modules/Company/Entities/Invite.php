<?php
declare(strict_types=1);

namespace Main\Modules\Company\Entities;

use Core\Entities\AbstractEntity;

class Invite Extends AbstractEntity
{
    /**
     * invite_code
     *
     * @var
     */
    protected $inviteCode;
    /**
     * name
     *
     * @var
     */
    protected $name;
    /**
     * email
     *
     * @var
     */
    protected $email;
    /**
     * token
     *
     * @var
     */
    protected $token;
    /**
     * companyCode
     *
     * @var
     */
    protected $companyCode;
    /**
     * permittedModules
     *
     * @var
     */
    protected $permittedModules;
    /**
     * Company
     *
     * @collection    \Main\Modules\Company\Collections\CompanyCollection
     * @factory       \Main\Modules\Company\Factories\CompanyFactory
     * @aggregateType entity
     * @var
     */
    protected $company;
    /**
     * createdAt
     *
     * @var
     */
    protected $createdAt;
    /**
     * updatedAt
     *
     * @var
     */
    protected $updatedAt;
}