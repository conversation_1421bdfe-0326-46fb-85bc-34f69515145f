<?php
declare(strict_types=1);

namespace Main\Modules\Company\Entities;

use Core\Entities\AbstractEntity;

class Company Extends AbstractEntity
{
    /**
     * company_code
     *
     * @var
     */
    protected $companyCode;
    /**
     * company_name
     *
     * @var
     */
    protected $companyName;
    /**
     * contact_person
     *
     * @var
     */
    protected $contactPerson;
    /**
     * zipcode
     *
     * @var
     */
    protected $zipcode;
    /**
     * balance
     *
     * @var
     */
    protected $balance;
    /**
     * countryCode
     *
     * @var
     */
    protected $countryCode;
    /**
     * attn
     *
     * @var
     */
    protected $attn;
    /**
     * website
     *
     * @var
     */
    protected $website;
    /**
     * address
     *
     * @var
     */
    protected $address;
    /**
     * companyLogo
     *
     * @var
     */
    protected $companyLogo;
    /**
     * city
     *
     * @var
     */
    protected $city;
    /**
     * languageCode
     *
     * @var
     */
    protected $languageCode;
    /**
     * Country
     *
     * @collection    \Main\Modules\Location\Collections\CountryCollection
     * @factory       \Main\Modules\Location\Factories\CountryFactory
     * @aggregateType entity
     * @var
     */
    protected $country;
    /**
     * Language
     *
     * @collection    \Main\Modules\Location\Collections\LanguageCollection
     * @factory       \Main\Modules\Location\Factories\LanguageFactory
     * @aggregateType entity
     * @var
     */
    protected $language;
    /**
     * vatNo
     *
     * @var
     */
    protected $vatNo;
    /**
     * phoneNo
     *
     * @var
     */
    protected $phoneNo;
    /**
     * createdAt
     *
     * @var
     */
    protected $createdAt;
    /**
     * updatedAt
     *
     * @var
     */
    protected $updatedAt;
}
