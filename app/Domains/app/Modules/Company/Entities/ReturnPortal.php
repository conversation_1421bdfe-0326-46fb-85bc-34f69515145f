<?php
declare(strict_types=1);

namespace Main\Modules\Company\Entities;

use Core\Entities\AbstractEntity;

class ReturnPortal Extends AbstractEntity
{
    /**
     * returnPortalCode
     *
     * @var
     */
    protected $returnPortalCode;
    /**
     * companyCode
     *
     * @var
     */
    protected $companyCode;
    /**
     * portalName
     *
     * @var
     */
    protected $portalName;
    /**
     * firstName
     *
     * @var
     */
    protected $firstName;
    /**
     * lastName
     *
     * @var
     */
    protected $lastName;
    /**
     * isActive
     *
     * @var
     */
    protected $isActive;
    /**
     * confirmationNote
     *
     * @var
     */
    protected $confirmationNote;
    /**
     * integrationCode
     *
     * @var
     */
    protected $integrationCode;
    /**
     * senderCountryCode
     *
     * @var
     */
    protected $senderCountryCode;
    /**
     * languageCode
     *
     * @var
     */
    protected $languageCode;
    /**
     * senderCountry
     *
     * @collection    \Main\Modules\Location\Collections\CountryCollection
     * @factory       \Main\Modules\Location\Factories\CountryFactory
     * @aggregateType entity
     * @var
     */
    protected $senderCountry;
    /**
     * language
     *
     * @collection    \Main\Modules\Location\Collections\LanguageCollection
     * @factory       \Main\Modules\Location\Factories\LanguageFactory
     * @aggregateType entity
     * @var
     */
    protected $language;
    /**
     * Company
     *
     * @collection    \Main\Modules\Company\Collections\CompanyCollection
     * @factory       \Main\Modules\Company\Factories\CompanyFactory
     * @aggregateType entity
     * @var
     */
    protected $company;
    /**
     * companyName
     *
     * @var
     */
    protected $companyName;
    /**
     * countryCode
     *
     * @var
     */
    protected $countryCode;
    /**
     * attn
     *
     * @var
     */
    protected $attn;
    /**
     * phone
     *
     * @var
     */
    protected $phone;
    /**
     * email
     *
     * @var
     */
    protected $email;
    /**
     * address
     *
     * @var
     */
    protected $address;
    /**
     * zipcode
     *
     * @var
     */
    protected $zipcode;
    /**
     * logo
     *
     * @var
     */
    protected $logo;
    /**
     * meta
     *
     * @var
     */
    protected $meta;
    /**
     * city
     *
     * @var
     */
    protected $city;
    /**
     * country
     *
     * @collection    \Main\Modules\Location\Collections\CountryCollection
     * @factory       \Main\Modules\Location\Factories\CountryFactory
     * @aggregateType entity
     * @var
     */
    protected $country;
    /**
     * pageTitle
     *
     * @var
     */
    protected $pageTitle;
    /**
     * content
     *
     * @var
     */
    protected $content;
    /**
     * createdAt
     *
     * @var
     */
    protected $createdAt;
    /**
     * updatedAt
     *
     * @var
     */
    protected $updatedAt;
}
