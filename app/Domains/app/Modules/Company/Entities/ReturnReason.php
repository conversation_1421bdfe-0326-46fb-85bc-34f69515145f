<?php
declare(strict_types=1);

namespace Main\Modules\Company\Entities;

use Core\Entities\AbstractEntity;

class ReturnReason Extends AbstractEntity
{
    /**
     * returnReasonCode
     *
     * @var
     */
    protected $returnReasonCode;
    /**
     * description
     *
     * @var
     */
    protected $description;
    /**
     * enableComment
     *
     * @var
     */
    protected $enableComment;
    /**
     * commentRequired
     *
     * @var
     */
    protected $commentRequired;
    /**
     * availableDays
     *
     * @var
     */
    protected $availableDays;
    /**
     * createdAt
     *
     * @var
     */
    protected $createdAt;
    /**
     * updatedAt
     *
     * @var
     */
    protected $updatedAt;
    /**
     * status
     *
     * @var
     */
    protected $status;
}
