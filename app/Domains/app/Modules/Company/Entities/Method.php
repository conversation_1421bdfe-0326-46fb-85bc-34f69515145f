<?php
declare(strict_types=1);

namespace Main\Modules\Company\Entities;

use Core\Entities\AbstractEntity;

class Method Extends AbstractEntity
{
    const PICK_TYPE = [
        'FIXED'      =>  'FIXED',
        'AUTOMATIC'  =>  'AUTOMATIC',
        'TABLE'      =>  'TABLE',
    ];
    /**
     * methodCode
     *
     * @var
     */
    protected $methodCode;
    /**
     * name
     *
     * @var
     */
    protected $name;
    /**
     * description
     *
     * @var
     */
    protected $description;
    /**
     * price
     *
     * @var
     */
    protected $price;
    /**
     * currencyCode
     *
     * @var
     */
    protected $currencyCode;
    /**
     * templateCode
     *
     * @var
     */
    protected $templateCode;
    /**
     * isActive
     *
     * @var
     */
    protected $isActive;
    /**
     * parcelShopId
     *
     * @var
     */
    protected $parcelshopCode;
    /**
     * priceType
     *
     * @var
     */
    protected $priceType;
    /**
     * template
     *
     * @collection    \Main\Modules\Shipment\Collections\TemplateCollection
     * @factory       \Main\Modules\Shipment\Factories\TemplateFactory
     * @aggregateType entity
     * @var
     */
    protected $template;
    /**
     * currency
     *
     * @collection    \Main\Modules\Setting\Collections\CurrencyCollection
     * @factory       \Main\Modules\Setting\Factories\CurrencyFactory
     * @aggregateType entity
     * @var
     */
    protected $currency;
    /**
     * createdAt
     *
     * @var
     */
    protected $createdAt;
    /**
     * updatedAt
     *
     * @var
     */
    protected $updatedAt;
}
