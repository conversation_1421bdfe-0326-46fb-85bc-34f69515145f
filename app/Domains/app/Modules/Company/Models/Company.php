<?php

declare(strict_types=1);

namespace Main\Modules\Company\Models;

use Core\Models\AbstractModel;
use Main\Modules\User\Models\User;
use Main\Modules\Transaction\Models\Invoice;
use Main\Modules\Platforms\Models\Integration;

class Company extends AbstractModel
{
    protected $primaryKey = 'company_code';
    public $incrementing = false;
    protected $fillable = [
        'company_code',
        'company_name',
        'contact_person',
        'country_code',
        'city',
        'vat_no',
        'phone_no',
        'zipcode',
        'language_code',
        'attn',
        'address',
        'website',
        'balance',
        'company_logo'
    ];

    protected array $nullables = ['company_logo'];

    protected array $relationships = [
        'country',
        'language',
        'gatewayIntegrations'
    ];

    public function country()
    {
        return $this->belongsTo(\Main\Modules\Location\Models\Country::class, 'country_code', 'country_code');
    }

    public function language()
    {
        return $this->belongsTo(\Main\Modules\Location\Models\Language::class, 'language_code', 'language_code');
    }

    public function gatewayIntegrations()
    {
        return $this->hasMany(\Main\Modules\Payment\Models\GatewayIntegration::class, 'company_code', 'company_code');
    }
    public function merchant()
    {
        return $this->belongsTo(User::class, 'company_code', 'company_code');
    }

    public function invoice()
    {
        return $this->hasMany(Invoice::class, 'company_code', 'company_code');
    }

    public function integrations()
    {
        return $this->hasMany(Integration::class, 'company_code', 'company_code');
    }

    public function personalizedMessage()
    {
        return $this->hasMany(PersonalizedMessage::class, 'company_code', 'company_code');
    }
}
