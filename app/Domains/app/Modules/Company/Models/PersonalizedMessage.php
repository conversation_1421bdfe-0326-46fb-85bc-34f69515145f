<?php
declare(strict_types=1);

namespace Main\Modules\Company\Models;

use Core\Models\AbstractModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Main\Modules\Location\Models\Country;
use Main\Modules\Platforms\Models\Integration;
use Main\Modules\Setting\Models\MessageHook;

class PersonalizedMessage extends AbstractModel
{
    protected $primaryKey = 'message_code';
    public $incrementing = false;
    protected $fillable = ['message_code', 'message_template_code', 'message_hook_code', 'type', 'is_receiver_country_specific', 'receiver_country_code', 'is_integration_specific', 'integration_code', 'send_option', 'send_time', 'message_content', 'company_code','bcc','subject'];

    protected array $nullables = ['subject', 'bcc', 'receiver_country_code', 'send_time', 'integration_code'];
    protected array $relationships = ['messageHook','receiverCountry', 'integration', 'company'];
    const TEMPLATE_TYPE = [
        'SMS'=>'SMS',
        'EMAIL'=>'EMAIL',
    ];

    const SEND_OPTION = [
        'IMMEDIATELY'=>'IMMEDIATELY',
        'SPECIFIC_TIME'=>'SPECIFIC_TIME',
    ];
    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [];

    public function messageHook(): BelongsTo
    {
        return $this->belongsTo(MessageHook::class, 'message_hook_code', 'message_hook_code');
    }

    public function receiverCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'receiver_country_code', 'country_code');
    }

    public function integration(): BelongsTo
    {
        return $this->belongsTo(Integration::class, 'integration_code', 'integration_code');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_code', 'company_code');
    }
}
