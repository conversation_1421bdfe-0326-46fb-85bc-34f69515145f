<?php
declare(strict_types=1);

namespace Main\Modules\Company\Models;

use Core\Models\AbstractModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Main\Modules\Company\Models\ReturnPortal;

class ReturnReason extends AbstractModel
{
    protected $primaryKey = 'return_reason_code';
    public $incrementing = false;
    protected $fillable = ['return_reason_code', 'description', 'return_portal_code', 'enable_comment', 'available_days', 'comment_required','status'];

    protected array $relationships = ['returnPortal'];

    public function returnPortal(): BelongsTo
    {
        return $this->belongsTo(ReturnPortal::class, 'return_portal_code', 'return_portal_code');
    }
}
