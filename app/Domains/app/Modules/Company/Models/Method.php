<?php

declare(strict_types=1);

namespace Main\Modules\Company\Models;

use Core\Models\AbstractModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Main\Modules\Company\Models\ReturnPortal;
use Main\Modules\Setting\Models\Currency;
use Main\Modules\Shipment\Models\Template;

class Method extends AbstractModel
{
    protected $primaryKey = 'method_code';
    public $incrementing = false;
    protected $fillable = [
        'method_code',
        'name',
        'description',
        'return_portal_code',
        'template_code',
        'is_active',
        'parcelshop_code',
        'price_type',
        'currency_code',
        'price'
    ];

    protected array $relationships = [
        'returnPortal',
        'template',
        'currency'
    ];

    public function returnPortal(): BelongsTo
    {
        return $this->belongsTo(ReturnPortal::class, 'return_portal_code', 'return_portal_code');
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'currency_code', 'currency_code');
    }

    public function template(): BelongsTo
    {
        return $this->belongsTo(Template::class, 'template_code', 'template_code');
    }
}
