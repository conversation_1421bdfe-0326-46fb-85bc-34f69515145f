<?php

declare(strict_types=1);

namespace Main\Modules\Company\Models;

use Core\Models\AbstractModel;
use Main\Modules\Location\Models\Country;
use Main\Modules\Location\Models\Language;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ReturnPortal extends AbstractModel
{
    protected $primaryKey = 'return_portal_code';
    public $incrementing = false;
    protected $fillable = [
        'company_code',
        'return_portal_code',
        'portal_name',
        'integration_code',
        'confirmation_note',
        'sender_country_code',
        'language_code',
        'is_active',
        'logo',
        'meta',
        'company_name',
        'first_name',
        'last_name',
        'attn',
        'address',
        'zipcode',
        'city',
        'country_code',
        'phone',
        'email',
        'page_title',
        'content'
    ];

    protected array $nullables = ['confirmation_note', 'content', 'logo'];

    protected array $relationships = [
        'company',
        'senderCountry',
        'language',
        'integration',
        'country'
    ];

    protected $casts = [
        'meta' => 'array'
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(\Main\Modules\Company\Models\Company::class, 'company_code', 'company_code');
    }

    public function integration(): BelongsTo
    {
        return $this->belongsTo(\Main\Modules\Platforms\Models\Integration::class, 'integration_code', 'integration_code');
    }

    public function senderCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'sender_country_code', 'country_code');
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_code', 'country_code');
    }

    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class, 'language_code', 'language_code');
    }
}
