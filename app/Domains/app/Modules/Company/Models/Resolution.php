<?php
declare(strict_types=1);

namespace Main\Modules\Company\Models;

use Core\Models\AbstractModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Main\Modules\Company\Models\ReturnPortal;

class Resolution extends AbstractModel
{
    protected $primaryKey = 'resolution_code';
    public $incrementing = false;
    protected $fillable = ['resolution_code', 'name', 'description', 'return_portal_code', 'available_days', 'status'];

    protected array $relationships = ['returnPortal'];

    public function returnPortal(): BelongsTo
    {
        return $this->belongsTo(ReturnPortal::class, 'return_portal_code', 'return_portal_code');
    }
}
