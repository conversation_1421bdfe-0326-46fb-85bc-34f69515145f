<?php
declare(strict_types=1);

namespace Main\Modules\Company\Models;

use Core\Models\AbstractModel;

class Invite extends AbstractModel
{
    protected $primaryKey = 'invite_code';
    public $incrementing = false;
    protected $fillable = ['invite_code', 'company_code', 'name', 'email', 'token', 'permitted_modules'];

    protected array $relationships = [
        'company',
    ];

    protected $casts = [
        'permitted_modules'=>'array'
    ];

    public function company()
    {
        return $this->belongsTo(\Main\Modules\Company\Models\Company::class, 'company_code', 'company_code');
    }
}