<?php

declare(strict_types=1);

namespace Main\Modules\Company\Services;

use Illuminate\Support\Facades\Auth;
use Core\Services\AbstractService;
use Core\Contracts\Request\Request;
use Core\Contracts\Response\Response;
use Core\Exceptions\AuthenticationException;
use Core\Exceptions\ValidationException;
use Main\Modules\Company\Authentication\PersonalizedMessageAuthentication;
use Main\Modules\Company\Validations\PersonalizedMessageValidation;
use Main\Modules\Company\Repositories\PersonalizedMessageRepository;
use Main\Modules\Company\Response\PersonalizedMessageResponse;
use Main\Modules\Company\Models\PersonalizedMessage;

class PersonalizedMessageService extends AbstractService
{
    public function __construct(
        PersonalizedMessageValidation $validator,
        PersonalizedMessageRepository $repository,
        PersonalizedMessageResponse $response,
        PersonalizedMessage $model,
        PersonalizedMessageAuthentication $authenticator
    ) {
        $this->validator = $validator;
        $this->repository = $repository;
        $this->model = $model;
        $this->response = $response;
        $this->authenticator = $authenticator;
    }

    public function index(Request $request): Response
    {
        try {
            $this->authenticator->authenticate();
            $request = $request->get();
            $request['conditions']['company_code'] = Auth::user()->company_code;
            $data = $this->repository->index($request);
            $this->response->setData($data->toArray());
        } catch (AuthenticationException $e) {
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch (\Exception $e) {
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode(400);
        }
        return $this->response;
    }

    /**
     * @param Request $request
     *
     * @return Response
     */
    public function store(Request $request): Response
    {
        try {
            $checkPersonalizedCreate = $this->checkPersonalizedEmailCreate($request->get());
            if ($checkPersonalizedCreate) {
                $this->response->setData(['error' => 'Tracking Message Already Set']);
                $this->response->setCode(400);
                return $this->response;
            } else {
                $this->authenticator->authenticate();
                $request->add('company_code', Auth::user()->company_code);
                $response = parent::store($request);
                $this->response->setData($response->getData());
                $this->response->setCode($response->code());
            }
        } catch (AuthenticationException $e) {
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch (ValidationException $e) {
            $this->response->setData(['error' => $e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch (\Exception $e) {
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode(400);
        }
        return $this->response;
    }
    public function update(Request $request, int $id): Response
    {
        try {
            $checkPersonalizedCreate = $this->checkPersonalizedEmailAlreadyCreated($request->get(), $id);
            if ($checkPersonalizedCreate) {
                $this->response->setData(['error' => 'Tracking Message Already Set']);
                $this->response->setCode(400);
                return $this->response;
            } else {
                if ($this->authenticator != null) {
                    $this->authenticator->authenticate();
                }
                $this->validator->update()->validate($request);
                $data = $this->repository->update($request->getDataForModel(), $request->get(), $id);
                $this->response->setData($data->toArray());
            }
        } catch (AuthenticationException $e) {
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch (ValidationException $e) {
            $this->response->setData(['error' => $e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch (\Exception $e) {
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode(400);
        }

        //Tracking and Store log when update
        // $this->logAction((int)$request->get('updated_by'), $id, 'update');

        return $this->response;
    }
    public function checkPersonalizedEmailCreate($request)
    {
        $data = $this->model->where([
            ['is_receiver_country_specific', '=', $request['is_receiver_country_specific']],

            ['is_integration_specific', '=', $request['is_integration_specific']],
            ['message_hook_code', '=', $request['message_hook_code']],
            ['company_code', '=', Auth::user()->company_code]
        ]);
        if ($request['is_receiver_country_specific'] == true) {
            $data->where([['receiver_country_code', '=', $request['receiver_country_code']]]);
        }

        if ($request['is_integration_specific'] == true) {
            $data->where([['integration_code', '=', $request['integration_code']]]);
        }
        $result = $data->exists();
        return $result;
    }
    public function checkPersonalizedEmailAlreadyCreated($request, $id)
    {
        $data  = $this->model->where([
            ['is_receiver_country_specific', '=', $request['is_receiver_country_specific']],
            ['is_integration_specific', '=', $request['is_integration_specific']],
            ['company_code', '=', Auth::user()->company_code]
        ]);

        if ($request['is_receiver_country_specific']) {
            $data->where([['receiver_country_code', '=', $request['receiver_country_code']]]);
        }

        if ($request['is_integration_specific']) {
            $data->where([['integration_code', '=', $request['integration_code']]]);
        }

        $result = $data->where('message_code', '!=', $id)
            ->when($request['message_hook_code'], function ($query) use ($request) {
                $query->where('message_hook_code', $request['message_hook_code']);
            })->whereNull('deleted_at')->exists();
        return $result;
    }
}
