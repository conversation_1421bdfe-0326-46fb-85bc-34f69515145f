<?php
declare(strict_types=1);

namespace Main\Modules\Company\Services;

use Illuminate\Support\Facades\Auth;

use Core\Services\AbstractService;
use Core\Contracts\Request\Request;
use Core\Contracts\Response\Response;
use Core\Exceptions\AuthenticationException;
use Core\Exceptions\NotFoundException;
use Core\Exceptions\ValidationException;

use Main\Modules\Company\Authentication\ReturnPortalAuthentication;
use Main\Modules\Company\Validations\MethodValidation;
use Main\Modules\Company\Repositories\MethodRepository;
use Main\Modules\Company\Response\MethodResponse;
use Main\Modules\Company\Models\Method;

class MethodService extends AbstractService
{
    public function __construct(
        MethodValidation $validator,
        MethodRepository $repository,
        MethodResponse $response,
        Method $model,
        ReturnPortalAuthentication $authenticator
    ) {
        $this->validator = $validator;
        $this->repository = $repository;
        $this->model = $model;
        $this->response = $response;
        $this->authenticator = $authenticator;
    }


    public function methods(int $returnPortalCode, Request $request): Response
    {
        try {
            $this->authenticator->authenticate();
            $request = $request->get();
            $request['paginate'] = false;
            $request['conditions']['return_portal_code'] = $returnPortalCode;
            $request['conditions']['is_active'] = true;
            $data = $this->repository->index($request);
            $this->response->setData($data->toArray());
        } catch(AuthenticationException $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch(\Exception $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode(400);
        }

        return $this->response;
    }

    public function createMethod(int $returnPortalCode, Request $request): Response
    {
        try {
            $this->authenticator->authenticate();
            $request->add('return_portal_code', $returnPortalCode);
            $this->validator->store()->validate($request);

            $model = $this->repository->store($this->validator->validated(), $request->get());
            $this->response->setData($model->toArray());
        } catch(AuthenticationException $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch(ValidationException $e) {
            $this->response->setData(['error'=>$e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch(\Exception $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode(400);
        }

        return $this->response;
    }

    public function updateMethod(int $returnPortalCode, int $methodCode, Request $request): Response
    {
        try {
            $this->authenticator->authenticate();
            $request->add('return_portal_code', $returnPortalCode);
            $this->validator->update()->validate($request);

            $model = $this->repository->update($this->validator->validated(), $request->get(), $methodCode);
            $this->response->setData($model->toArray());
        } catch(AuthenticationException $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch(ValidationException $e) {
            $this->response->setData(['error'=>$e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch(\Exception $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode(400);
        }

        return $this->response;
    }

    public function Method(int $methodCode, Request $request): Response
    {
        try {
            $this->authenticator->authenticate();
            $model = $this->repository->show($request->get(), $methodCode);
            $this->response->setData($model->toArray());
        } catch(AuthenticationException $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch(ValidationException $e) {
            $this->response->setData(['error'=>$e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch(\Exception $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode(400);
        }

        return $this->response;
    }

    public function deleteMethod(int $methodCode, Request $request): Response
    {
        try {
            $this->authenticator->authenticate();
            $response = parent::destroy($request, $methodCode);
            $this->response->setData($response->toArray());
        } catch(AuthenticationException $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch(ValidationException $e) {
            $this->response->setData(['error'=>$e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch(\Exception $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode(400);
        }

        return $this->response;
    }
}
