<?php
declare(strict_types=1);

namespace Main\Modules\Company\Services;

use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

use Core\Services\AbstractService;
use Core\Contracts\Request\Request;
use Core\Contracts\Response\Response;
use Core\Exceptions\AuthenticationException;
use Core\Exceptions\ValidationException;

use Main\Modules\Company\Validations\InviteValidation;
use Main\Modules\Company\Repositories\InviteRepository;
use Main\Modules\Company\Response\InviteResponse;
use Main\Modules\Company\Models\Invite;
use Main\Modules\Company\Authentication\CompanyAuthentication;

use Main\Jobs\SendInvite;
use Main\Mail\InviteCreated;

class InviteService extends AbstractService
{
    public function __construct(
        InviteValidation $validator,
        InviteRepository $repository,
        InviteResponse $response,
        Invite $model,
        CompanyAuthentication $authenticator
    ) {
        $this->validator = $validator;
        $this->repository = $repository;
        $this->model = $model;
        $this->response = $response;
        $this->authenticator = $authenticator;
    }

    /**
     * @param Request $request
     *
     * @return Response
     */
    public function store(Request $request): Response
    {
        try
        {
            $this->authenticator->authenticate();

            do{
                $token = Str::random(16);
            } while($this->model->where('token', $token)->first());

            $request->add('token', $token);
            $request->add('company_code', Auth::user()->company_code);
            $response = parent::store($request);
            $responseData = $response->getData();

            if($response->code() == 200) {
                SendInvite::dispatch(
                    new InviteCreated(
                        $responseData['name'],
                        $responseData['email'],
                        $token,
                        $responseData['invite_code']
                    )
                );
            }
            $this->response->setData($responseData);
        } catch(AuthenticationException $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch(ValidationException $e) {
            $this->response->setData(['error'=>$e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch(\Exception $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode(400);
        }
        return $this->response;
    }

    public function getInvite(string $token): InviteResponse
    {
        try {
            $model = $this->repository->getInvite($token);
            // $data = $model != null? $model->toArray():[];
            $this->response->setData($model->toArray());
        } catch (\Exception $e){
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode($e->getCode());
        }
        return $this->response;
    }

    public function index(Request $request): Response
    {
        try
        {
            $this->authenticator->authenticate();

            $request = $request->get();
            $request['conditions']['company_code'] = Auth::user()->company_code;
            $data = $this->repository->index($request);
            $this->response->setData($data->toArray());
        } catch(AuthenticationException $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch(ValidationException $e) {
            $this->response->setData(['error'=>$e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch(\Exception $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode(400);
        }
        return $this->response;
    }
}
