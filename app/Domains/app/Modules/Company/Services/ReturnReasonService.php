<?php
declare(strict_types=1);

namespace Main\Modules\Company\Services;
use Core\Services\AbstractService;
use Core\Contracts\Request\Request;
use Core\Contracts\Response\Response;
use Core\Exceptions\ValidationException;
use Core\Exceptions\AuthenticationException;

use Main\Modules\Company\Models\ReturnReason;
use Main\Modules\Company\Response\ReturnReasonResponse;
use Main\Modules\Company\Validations\ReturnReasonValidation;
use Main\Modules\Company\Repositories\ReturnReasonRepository;
use Main\Modules\Company\Authentication\ReturnPortalAuthentication;

class ReturnReasonService extends AbstractService
{
    public function __construct(
        ReturnReasonValidation $validator,
        ReturnReasonRepository $repository,
        ReturnReasonResponse $response,
        ReturnReason $model,
        ReturnPortalAuthentication $authenticator
    ) {
        $this->validator = $validator;
        $this->repository = $repository;
        $this->model = $model;
        $this->response = $response;
        $this->authenticator = $authenticator;
    }


    public function returnPortalReasons(int $returnPortalCode, Request $request): Response
    {
        try {
            $this->authenticator->authenticate();

            $request = $request->get();
            $request['paginate'] = false;
            $request['conditions']['return_portal_code'] = $returnPortalCode;
            $data = $this->repository->index($request);
            $this->response->setData($data->toArray());
        } catch(AuthenticationException $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch(\Exception $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode(400);
        }

        return $this->response;
    }

    public function createReturnReason(int $returnPortalCode, Request $request): Response
    {
        try {
            $this->authenticator->authenticate();
            $request->add('return_portal_code', $returnPortalCode);
            $this->validator->store()->validate($request);

            $model = $this->repository->store($this->validator->validated(), $request->get());
            $this->response->setData($model->toArray());
        } catch(AuthenticationException $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch(ValidationException $e) {
            $this->response->setData(['error'=>$e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch(\Exception $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode(400);
        }

        return $this->response;
    }

    public function updateReturnReason(int $returnReasonCode, Request $request): Response
    {
        try {
            $this->authenticator->authenticate();
            $this->validator->update()->validate($request);

            $model = $this->repository->update($this->validator->validated(), $request->get(), $returnReasonCode);
            $this->response->setData($model->toArray());
        } catch(AuthenticationException $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch(ValidationException $e) {
            $this->response->setData(['error'=>$e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch(\Exception $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode(400);
        }

        return $this->response;
    }

    public function returnReason(int $returnReasonCode, Request $request): Response
    {
        try {
            $this->authenticator->authenticate();
            $model = $this->repository->show($request->get(), $returnReasonCode);
            $this->response->setData($model->toArray());
        } catch(AuthenticationException $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch(ValidationException $e) {
            $this->response->setData(['error'=>$e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch(\Exception $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode(400);
        }

        return $this->response;
    }

    public function deleteReturnReason(int $returnReasonCode, Request $request): Response
    {
        try {
            $this->authenticator->authenticate();
            $response = parent::destroy($request, $returnReasonCode);
            $this->response->setData($response->toArray());
        } catch(AuthenticationException $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch(ValidationException $e) {
            $this->response->setData(['error'=>$e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch(\Exception $e) {
            $this->response->setData(['error'=>$e->getMessage()]);
            $this->response->setCode(400);
        }

        return $this->response;
    }
}
