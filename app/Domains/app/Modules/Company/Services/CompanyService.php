<?php

declare(strict_types=1);

namespace Main\Modules\Company\Services;

use Illuminate\Support\Str;
use Core\Services\AbstractService;
use Core\Contracts\Request\Request;

use Illuminate\Support\Facades\Auth;
use Core\Contracts\Response\Response;
use Core\Packages\Media\MediaPackage;
use Main\Modules\Setting\Models\Module;
use Core\Exceptions\ValidationException;
use Main\Modules\Company\Models\Company;
use Main\Modules\User\Requests\UserRequest;
use Main\Modules\User\Services\UserService;
use Main\Modules\Company\Response\CompanyResponse;
use Main\Modules\Setting\Requests\PickSettingRequest;
use App\Domains\app\Services\BalanceCalculationService;
use Main\Modules\Company\Validations\CompanyValidation;
use Main\Modules\Company\Repositories\CompanyRepository;
use Main\Modules\Transaction\Services\TransactionService;
use Main\Modules\Setting\Repositories\PickSettingRepository;
use Main\Modules\Company\Authentication\CompanyAuthentication;

class CompanyService extends AbstractService
{
    public UserService $userService;
    public UserRequest $userRequest;
    public Module $module;
    public PickSettingRequest $pickSettingRequest;
    public PickSettingRepository $pickSettingRepository;
    public BalanceCalculationService $balanceCalculationService;

    protected MediaPackage $mediaPackage;

    public function __construct(
        CompanyValidation $validator,
        CompanyRepository $repository,
        CompanyResponse $response,
        Company $model,
        private TransactionService $transaction,
        UserService $userService,
        UserRequest $userRequest,
        Module $module,
        CompanyAuthentication $authenticator,
        MediaPackage $package,
        PickSettingRepository $pickSettingRepository,
        PickSettingRequest $pickSettingRequest,
        BalanceCalculationService $balanceCalculationService
    ) {
        $this->validator = $validator;
        $this->repository = $repository;
        $this->model = $model;
        $this->response = $response;
        $this->userService = $userService;
        $this->userRequest = $userRequest;
        $this->module = $module;
        $this->authenticator = $authenticator;
        $this->mediaPackage = $package;
        $this->pickSettingRepository = $pickSettingRepository;
        $this->pickSettingRequest = $pickSettingRequest;
        $this->balanceCalculationService = $balanceCalculationService;
    }

    /**
     * @param Request $request
     *
     * @return [type]
     */
    public function register(Request $request)
    {
        $this->repository->startTransaction();

        try {
            $this->validator->register()->validate($request);
            $company = $this->repository->store($request->getDataForModel(), $request->get());
            $companyCode = $company->toArray()[$this->model->getKeyName()];

            $modules = $this->module->get()->pluck('module_code');
            // create user
            $this->userRequest->set(
                [
                    'name'  =>  $request->get('contact_person'),
                    'email' => $request->get('email'),
                    'password' => $request->get('password'),
                    'role' => \Main\Modules\User\Entities\User::ROLES['COMPANY_ADMIN'],
                    'company_code' => $companyCode,
                    'remember_token' => Str::random(10),
                    'permitted_modules' => $modules
                ]
            );

            $data = $this->userService->register($this->userRequest);

            $this->pickSettingRequest->set([
                'company_code' => $companyCode,
                'capture_payment' => false,
                'choose_packaging' => false,
                'confirm_pick_amount_obtained' => false,
                'create_fulfillment' => false,
                'create_shipment' => false,
                'finish_when_items_picked' => false,
                'go_to_next_item' => false,
                'language_code' => $request->get('language_code'),
                'pick_sorting' => 'Bin',
                'play_sound' => false,
                'primary_action' => 'CreatePickPathList',
                'print_packing_slip' => false,
                'print_proforma_invoice' => false,
                'print_shipping_label' => false,
                'say_item_name' => false,
                'show_item_image' => true,
                'skip_summary' => false,
                'start_at_first_item' => false,
                'use_camera_on_mobile_devices' => false,
                'vibrate_when_scanning' => false,
                'warn_partial_pick' => false,
            ]);

            $this->pickSettingRepository->store($this->pickSettingRequest->getDataForModel(), $this->pickSettingRequest->get());

            $this->repository->endTransaction();
            $this->response->setData($company->toArray());
            $this->response->appendData('user', $data->getData());
        } catch (ValidationException $e) {
            $this->response->setData(['error' => $e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch (\Exception $e) {
            $this->repository->rollbackTransaction();
            $this->response->setData(['error' => $this->validator->getErrors()]);
            $this->response->setCode($e->getCode());
        }
        return $this->response;
    }

    public function update(Request $request, int $companyCode): Response
    {
        $fileName = '';
        $folderName = 'company_logos';

        try {
            $this->validator->update()->validate($request);

            if ('' !== $request->get('company_logo')) {
                $model = $this->repository->show($request, $companyCode);
                if ($model['company_file'] !== '') {
                    $fileName = $model['company_file'];
                }

                $fileName = $this->mediaPackage->uploadFile($request->get('company_logo'), $folderName, $fileName);
                $request->add('company_logo', $fileName);
            }

            $data = $this->repository->update($request->getDataForModel(), $request->get(), $companyCode);
            $this->response->setData($data->toArray());
        } catch (ValidationException $e) {
            $this->response->setData(['error' => $e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch (\Exception $e) {
            $this->repository->rollbackTransaction();
            $this->response->setData(['error' => $this->validator->getErrors()]);
            $this->response->setCode($e->getCode());
        }

        return $this->response;
    }

    public function getCurrentCompany(Request $request): object
    {
        $companyCode = Auth::user()->company_code;
        $request = $request->get();
        $request['conditions']['company_code'] = $companyCode;
        $data = $this->repository->index($request);
        $data_array = $data->toArray();
        unset($data_array['entities'][0]['balance']);


        $balance = $this->balanceCalculationService->calculateBalance($companyCode);
        $data_array['entities'][0]['balance'] = $balance ? round($balance, 2) : 0;

        $this->response->setData($data_array);
        return $this->response;
    }

    public function getCompanyByCode($companyCode)
    {
        $company = $this->repository->findById($companyCode, []);
        return $company;
    }
}
