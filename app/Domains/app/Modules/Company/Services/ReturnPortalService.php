<?php

declare(strict_types=1);

namespace Main\Modules\Company\Services;

use Core\Contracts\Request\Request;
use Core\Contracts\Response\Response;
use Core\Services\AbstractService;
use Core\Exceptions\AuthenticationException;
use Core\Exceptions\ValidationException;
use Core\Packages\Media\MediaPackage;
use Illuminate\Support\Facades\Auth;

use Illuminate\Support\Facades\Storage;
use Main\Modules\Company\Authentication\ReturnPortalAuthentication;
use Main\Modules\Company\Models\Company;
use Main\Modules\Company\Validations\ReturnPortalValidation;
use Main\Modules\Company\Repositories\ReturnPortalRepository;
use Main\Modules\Company\Response\ReturnPortalResponse;
use Main\Modules\Company\Models\ReturnPortal;
use Main\Modules\Company\Repositories\ResolutionRepository;
use Main\Modules\Company\Repositories\ReturnReasonRepository;

class ReturnPortalService extends AbstractService
{
    protected MediaPackage $mediaPackage;
    protected ReturnReasonRepository $returnReasonRepository;
    protected ResolutionRepository $resolutionRepository;

    public function __construct(
        ReturnPortalValidation $validator,
        ReturnPortalRepository $repository,
        ReturnPortalResponse $response,
        ReturnPortal $model,
        ReturnPortalAuthentication $authenticator,
        MediaPackage $mediaPackage,
        ReturnReasonRepository $returnReasonRepository,
        ResolutionRepository $resolutionRepository
    ) {
        $this->validator = $validator;
        $this->repository = $repository;
        $this->model = $model;
        $this->response = $response;
        $this->authenticator = $authenticator;
        $this->mediaPackage = $mediaPackage;
        $this->returnReasonRepository = $returnReasonRepository;
        $this->resolutionRepository = $resolutionRepository;
    }

    /**
     * @param Request $request
     *
     * @return Response
     */
    public function index(Request $request): Response
    {
        try {
            $this->authenticator->authenticate();

            $request = $request->get();
            $request['conditions']['company_code'] = Auth::user()->company_code;
            $data = $this->repository->index($request);
            $this->response->setData($data->toArray());
        } catch (AuthenticationException $e) {
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch (ValidationException $e) {
            $this->response->setData(['error' => $e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch (\Exception $e) {
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode(400);
        }
        return $this->response;
    }

    /**
     * @param Request $request
     *
     * @return Response
     */
    public function store(Request $request): Response
    {
        try {
            $this->authenticator->authenticate();
            $this->validator->store()->validate($request);
            $request->add('company_code', Auth::user()->company_code);
            $request->add('company_name', Auth::user()->company->company_name);
            $request->add('attn', Auth::user()->company->contact_person);

            $nameParts = explode(' ', Auth::user()->company->contact_person, 2);
            $request->add('first_name', $nameParts[0]);
            $request->add('last_name', isset($nameParts[1]) ? $nameParts[1] : 'Team');
            $request->add('zipcode', Auth::user()->company->zipcode);
            $request->add('city', Auth::user()->company->city);
            $request->add('country_code', Auth::user()->company->country_code);
            $request->add('phone', Auth::user()->company->phone_no);
            $request->add('address', Auth::user()->company->address);
            $request->add('email', Auth::user()->email);

            $request->add('page_title', 'Start your return');
            $request->add('content', 'Help us find your order. Fill out the form below.');

            $this->response = parent::store($request);

            $returnPortal = $this->response->toArray();
            $returnReasons = [
                "Arrived damaged or defective" => false,
                "Arrived too late" => false,
                "Changed my mind" => false,
                "Did not fit" => false,
                "Did not match the description" => false,
                "Did not meet expectations" => false,
                "Found a better price somewhere else" => false,
                "No reason" => false,
                "Other (comment required)" => true,
                "Size too big" => false,
                "Size too small" => false,
                "Wrong item was sent" => false,
                "Wrong size ordered" => false
            ];

            foreach ($returnReasons as $returnReason => $enableComment) {
                $this->returnReasonRepository->store([
                    'return_portal_code' => $returnPortal['return_portal_code'],
                    'available_days' => 0,
                    'enable_comment' => $enableComment,
                    'comment_required' => $enableComment,
                    'description' => $returnReason
                ], $request->get());
            }

            $resultionTypes = [
                'Money back',
                'Item exchange',
                'Store credit',
            ];

            foreach ($resultionTypes as $resolutionType) {
                $this->resolutionRepository->store([
                    'return_portal_code' => $returnPortal['return_portal_code'],
                    'name' => $resolutionType,
                    'description' => null,
                    'available_days' => 0
                ], $request->get());
            }
        } catch (AuthenticationException $e) {
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode($e->getStatusCode());
        } catch (ValidationException $e) {
            $this->response->setData(['error' => $e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch (\Exception $e) {
            $this->response->setData(['error' => $e->getMessage()]);
            $this->response->setCode(400);
        }
        return $this->response;
    }

    public function updateReceiver(int $returnPortalCode, Request $request): Response
    {
        $fileName = '';

        try {
            $this->validator->updateReceiver()->validate($request);
            $validated = $this->validator->validated();

            $model = $this->repository->show($request->get(), $returnPortalCode);
            $model = $model->toArray();

            $company = Company::whereCompanyCode($model['company_code'])->first();
            $companyUrl = $company ? $company->website : null;
            $portalUrl = $request->get('company_logo_url') ? $request->get('company_logo_url') : ($model['meta']['company_logo_url'] ?? null);
            $logoName =  $model['meta']['logo_name'] ?? null;

            if ($request->get('logo') !== '') {
                if ($model['logo'] !== null) {
                    $fileName = $model['logo'];
                }

                $fileName = $fileName == "" ? $model['portal_name'] . '-' . $model['company_code'] . '.png' : $fileName;
                $image = $request->get('logo');  // your base64 encoded
                $logoName = $request->get('logo_name') ?? null;
                $image = str_replace('data:image/png;base64,', '', $image);
                $image = str_replace(' ', '+', $image);
                Storage::disk('s3-return-portal')->put('company_logos/' . $fileName, base64_decode($image));
                $validated['logo'] = $fileName;
            }

            if ($request->get('logo_name') == "" || $request->get('logo_name') == null) {
                $validated['logo'] = null;
                $logoName =  null;
            }

            $validated['meta'] = [
                'company_url' => $companyUrl,
                'company_logo_url' => $portalUrl,
                'logo_name' => $logoName,
            ];

            unset($validated['company_logo_url']);
            unset($validated['logo_name']);

            if ($validated['attn'] && $validated['attn'] !== '') {
                $nameParts = explode(' ', $validated['attn'], 2);

                $validated['first_name'] = $nameParts[0] ?? 'Team ';
                $validated['last_name'] = isset($nameParts[1]) ? $nameParts[1] : 'Team';
            }

            $data = $this->repository->updateReceiver($returnPortalCode, $validated);
            $this->response->setData(['status' => $data]);
        } catch (ValidationException $e) {
            $this->response->setData(['error' => $e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch (\Exception $e) {
            $this->repository->rollbackTransaction();
            $this->response->setData(['error' => $this->validator->getErrors()]);
            $this->response->setCode($e->getCode());
        }

        return $this->response;
    }

    public function updateText(int $returnPortalCode, Request $request): Response
    {
        try {
            $this->validator->updateText()->validate($request);
            $data = $this->repository->updateText($returnPortalCode, $this->validator->validated());
            $this->response->setData(['status' => $data]);
        } catch (ValidationException $e) {
            $this->response->setData(['error' => $e->errors()]);
            $this->response->setCode($e->getStatusCode());
        } catch (\Exception $e) {
            $this->repository->rollbackTransaction();
            $this->response->setData(['error' => $this->validator->getErrors()]);
            $this->response->setCode($e->getCode());
        }

        return $this->response;
    }
}
