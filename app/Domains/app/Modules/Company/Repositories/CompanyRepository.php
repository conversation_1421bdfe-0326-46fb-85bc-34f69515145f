<?php

namespace Main\Modules\Company\Repositories;

use Exception;
use Core\Exceptions\NotFoundException;
use Main\Modules\Company\Models\Company;
use Core\Repositories\AbstractRepository;
use Main\Modules\Company\Factories\CompanyFactory;
use App\Domains\app\Services\BalanceCalculationService;
use Main\Modules\Company\Collections\CompanyCollection;

class CompanyRepository extends AbstractRepository
{
    private BalanceCalculationService $balanceService;

    /**
     * @param CompanyFactory    $factory
     * @param CompanyCollection $collection
     * @param Company           $model
     * @param BalanceCalculationService $balanceService
     */
    public function __construct(
        CompanyFactory $factory,
        CompanyCollection $collection,
        Company $model,
        BalanceCalculationService $balanceService
    ) {
        $this->factory = $factory;
        $this->collection = $collection;
        $this->model = $model;
        $this->balanceService = $balanceService;
    }

    public function getBalance(int $companyCode, $status = null): float|null
    {
        $availableBalance = null;
        $model = $this->model->where('company_code', '=', $companyCode)->first();

        if ($model) {
            $availableBalance = $this->balanceService->calculateBalance($companyCode, $status);
        }

        return $availableBalance;
    }

}
