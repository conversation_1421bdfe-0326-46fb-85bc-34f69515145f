<?php

namespace Main\Modules\Company\Repositories;

use Core\Exceptions\NotFoundException;
use Core\Repositories\AbstractRepository;

use Main\Modules\Company\Factories\PersonalizedMessageFactory;
use Main\Modules\Company\Collections\PersonalizedMessageCollection;
use Main\Modules\Company\Models\PersonalizedMessage;
use Main\Modules\Setting\Models\MessageHook;

class PersonalizedMessageRepository extends AbstractRepository
{
    /**
     * @param PersonalizedMessageFactory    $factory
     * @param PersonalizedMessageCollection $collection
     * @param PersonalizedMessage           $model
     */
    public function __construct(PersonalizedMessageFactory $factory, PersonalizedMessageCollection $collection, PersonalizedMessage $model)
    {
        $this->factory = $factory;
        $this->collection = $collection;
        $this->model = $model;
    }

    public function personalizedMessage(string $messageHookID, int $companyCode, string $type, int $countryCode=0, int $integrationCode=0): PersonalizedMessage|null
    {
        $model = null;
        if($countryCode != 0 && $integrationCode != 0) {
            $model = $this->personalizedMessageWithCountryAndIntegration($messageHookID, $companyCode, $countryCode, $integrationCode);
        }

        if($model == null && $countryCode != 0) {
            $model = $this->personalizedMessageWithCountry($messageHookID, $companyCode, $type, $countryCode);
        }

        if($model == null && $integrationCode != 0) {
            $model = $this->personalizedMessageWithIntegration($messageHookID, $companyCode, $type, $integrationCode);
        }

        if($model == null) {
            $model = $this->generalizedPersonalizedMessage($messageHookID, $companyCode, $type);
        }
        return $model;
    }
    public function getpersonalizedMessageForManualShipment(string $messageHookID, int $companyCode, string $type, int $countryCode=0, int $integrationCode=0): PersonalizedMessage|null
    {
        $messageHookTable = MessageHook::tableName();
        $data = $this->model
        ->leftJoin($messageHookTable, $messageHookTable.'.message_hook_code', '=', $this->model->getTable().'.message_hook_code')
        ->where([
            ['company_code', '=', $companyCode],
            [$messageHookTable.'.message_id', '=', $messageHookID],
            ['is_integration_specific', '=', false],
            ['type', '=', $type],
            ['receiver_country_code', '=', $countryCode]

        ])->first();
        if(!$data)
        {
            $data = $this->model
            ->leftJoin($messageHookTable, $messageHookTable.'.message_hook_code', '=', $this->model->getTable().'.message_hook_code')
            ->where([
                ['company_code', '=', $companyCode],
                [$messageHookTable.'.message_id', '=', $messageHookID],
                ['is_integration_specific', '=', false],
                ['type', '=', $type],
                ['is_receiver_country_specific', '=', false]

            ])->first();

        }
        return $data;
    }

    private function personalizedMessageWithCountryAndIntegration(string $messageHookID, int $companyCode, string $type, int $countryCode=0, int $integrationCode=0): PersonalizedMessage|Null
    {
        $messageHookTable = MessageHook::tableName();
        return $this->model
            ->leftJoin($messageHookTable, $messageHookTable.'.message_hook_code', '=', $this->model->getTable().'.message_hook_code')
            ->where(
                [
                ['company_code', '=', $companyCode],
                [$messageHookTable.'.message_id', '=', $messageHookID],
                ['type', '=', $type],
                ['receiver_country_code', '=', $countryCode],
                ['integration_code', '=', $integrationCode],
                ]
            )->first();
    }

    private function personalizedMessageWithCountry(string $messageHookID, int $companyCode, string $type, int $countryCode=0): PersonalizedMessage|Null
    {
        $messageHookTable = MessageHook::tableName();
        return $this->model
            ->leftJoin($messageHookTable, $messageHookTable.'.message_hook_code', '=', $this->model->getTable().'.message_hook_code')
            ->where(
                [
                ['company_code', '=', $companyCode],
                [$messageHookTable.'.message_id', '=', $messageHookID],
                ['type', '=', $type],
                ['receiver_country_code', '=', $countryCode],
                ]
            )->first();
    }

    private function personalizedMessageWithIntegration(string $messageHookID, int $companyCode, string $type, int $integrationCode=0): PersonalizedMessage|Null
    {
        $messageHookTable = MessageHook::tableName();
        return $this->model
            ->leftJoin($messageHookTable, $messageHookTable.'.message_hook_code', '=', $this->model->getTable().'.message_hook_code')
            ->where(
                [
                ['company_code', '=', $companyCode],
                [$messageHookTable.'.message_id', '=', $messageHookID],
                ['type', '=', $type],
                ['integration_code', '=', $integrationCode],
                ]
            )->first();
    }

    private function generalizedPersonalizedMessage(string $messageHookID, int $companyCode, string $type): PersonalizedMessage|Null
    {
        $messageHookTable = MessageHook::tableName();
        return $this->model
            ->leftJoin($messageHookTable, $messageHookTable.'.message_hook_code', '=', $this->model->getTable().'.message_hook_code')
            ->where(
                [
                ['company_code', '=', $companyCode],
                [$messageHookTable.'.message_id', '=', $messageHookID],
                ['type', '=', $type],
                ]
            )->first();
    }
}
