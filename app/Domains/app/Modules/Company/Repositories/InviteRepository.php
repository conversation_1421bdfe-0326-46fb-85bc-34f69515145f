<?php

namespace Main\Modules\Company\Repositories;

use Core\Repositories\AbstractRepository;

use Main\Modules\Company\Factories\InviteFactory;
use Main\Modules\Company\Collections\InviteCollection;
use Main\Modules\Company\Models\Invite;
use Main\Modules\Company\Entities\Invite as Entity;

class InviteRepository extends AbstractRepository
{
    /**
     * @param InviteFactory    $factory
     * @param InviteCollection $collection
     * @param Invite           $model
     */
    public function __construct(InviteFactory $factory, InviteCollection $collection, Invite $model)
    {
        $this->factory = $factory;
        $this->collection = $collection;
        $this->model = $model;
    }

    /**
     * @param string $token
     * 
     * @return Entity
     */
    public function getInvite(string $token): Entity|null
    {
        $model = $this->model->where('token', '=', $token)->first();
        if(!$model) {
            throw new \Core\Exceptions\EntityNotExistException();
        }
        return $this->factory->create($model->toArray());
    }

    /**
     * @param string $token
     * 
     * @return [type]
     */
    public function deleteToken(string $token)
    {
        return $this->model->where('token', '=', $token)->delete();
    }
}