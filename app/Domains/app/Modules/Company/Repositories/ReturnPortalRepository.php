<?php

namespace Main\Modules\Company\Repositories;

use Core\Repositories\AbstractRepository;

use Main\Modules\Company\Factories\ReturnPortalFactory;
use Main\Modules\Company\Collections\ReturnPortalCollection;
use Main\Modules\Company\Models\ReturnPortal;

class ReturnPortalRepository extends AbstractRepository
{
    /**
     * @param ReturnPortalFactory    $factory
     * @param ReturnPortalCollection $collection
     * @param ReturnPortal           $model
     */
    public function __construct(ReturnPortalFactory $factory, ReturnPortalCollection $collection, ReturnPortal $model)
    {
        $this->factory = $factory;
        $this->collection = $collection;
        $this->model = $model;
    }

    public function updateReceiver(int $returnPortalCode, array $arReceiverData)
    {
        return $this->model->where('return_portal_code', '=', $returnPortalCode)->update($arReceiverData);
    }

    public function updateText(int $returnPortalCode, array $arText)
    {
        return $this->model->where('return_portal_code', '=', $returnPortalCode)->update($arText);
    }
}