<?php

namespace Main\Modules\Company\Repositories;

use Core\Exceptions\NotFoundException;
use Core\Repositories\AbstractRepository;

use Main\Modules\Company\Factories\ResolutionFactory;
use Main\Modules\Company\Collections\ResolutionCollection;
use Main\Modules\Company\Models\Resolution;
class ResolutionRepository extends AbstractRepository
{
    /**
     * @param ResolutionFactory    $factory
     * @param ResolutionCollection $collection
     * @param Resolution           $model
     */
    public function __construct(ResolutionFactory $factory, ResolutionCollection $collection, Resolution $model)
    {
        $this->factory = $factory;
        $this->collection = $collection;
        $this->model = $model;
    }
}