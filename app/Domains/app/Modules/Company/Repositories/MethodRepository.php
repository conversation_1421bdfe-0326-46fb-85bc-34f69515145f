<?php

namespace Main\Modules\Company\Repositories;

use Core\Repositories\AbstractRepository;

use Main\Modules\Company\Factories\MethodFactory;
use Main\Modules\Company\Collections\MethodCollection;
use Main\Modules\Company\Models\Method;
class MethodRepository extends AbstractRepository
{
    /**
     * @param MethodFactory    $factory
     * @param MethodCollection $collection
     * @param Method           $model
     */
    public function __construct(MethodFactory $factory, MethodCollection $collection, Method $model)
    {
        $this->factory = $factory;
        $this->collection = $collection;
        $this->model = $model;
    }
}