<?php

namespace Main\Modules\Company\Repositories;

use Core\Exceptions\NotFoundException;
use Core\Repositories\AbstractRepository;

use Main\Modules\Company\Factories\ReturnReasonFactory;
use Main\Modules\Company\Collections\ReturnReasonCollection;
use Main\Modules\Company\Models\ReturnReason;
class ReturnReasonRepository extends AbstractRepository
{
    /**
     * @param ReturnReasonFactory    $factory
     * @param ReturnReasonCollection $collection
     * @param ReturnReason           $model
     */
    public function __construct(ReturnReasonFactory $factory, ReturnReasonCollection $collection, ReturnReason $model)
    {
        $this->factory = $factory;
        $this->collection = $collection;
        $this->model = $model;
    }
}