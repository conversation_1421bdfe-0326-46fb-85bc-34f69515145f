<?php

namespace Main\Modules\Company\Validations;

use Illuminate\Validation\Rule;

use Core\Validation\AbstractValidation;
use Core\Contracts\Validation\Validation;
use Main\Modules\Company\Models\ReturnPortal;

class ReturnReasonValidation extends AbstractValidation
{
    public function store(): Validation
    {
        $this->rules = [
            'return_portal_code' => [
                'required',
                'numeric',
                Rule::in(ReturnPortal::select('return_portal_code')->get()->pluck('return_portal_code'))
            ],
            'available_days' => 'numeric | nullable',
            'enable_comment' => 'boolean | nullable',
            'comment_required' => 'boolean | nullable',
            'description' => 'string | required'
        ];

        return $this;
    }

    public function update(): Validation
    {
        $this->rules = [
            'available_days' => 'numeric | nullable',
            'enable_comment' => 'boolean | nullable',
            'comment_required' => 'boolean | nullable',
            'description' => 'string | nullable',
            'status' => 'boolean'
        ];

        return $this;
    }
}
