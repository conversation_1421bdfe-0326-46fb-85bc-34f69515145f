<?php

namespace Main\Modules\Company\Validations;

use Illuminate\Validation\Rule;

use Core\Validation\AbstractValidation;
use Core\Contracts\Validation\Validation;
use Main\Modules\Company\Entities\PersonalizedMessage;
use Main\Modules\Location\Models\Country;
use Main\Modules\Platforms\Models\Integration;
use Main\Modules\Setting\Models\MessageHook;

class PersonalizedMessageValidation extends AbstractValidation
{
    public function store(): Validation
    {
        $this->rules = [
            'message_hook_code' => [
                'numeric',
                'required',
                Rule::in(MessageHook::select('message_hook_code')->get()->pluck('message_hook_code'))
            ],
            'type' => [
                'required',
                'string',
                Rule::in(PersonalizedMessage::TEMPLATE_TYPE)
            ],
            'is_receiver_country_specific' => 'boolean',
            'receiver_country_code' => [
                'numeric',
                Rule::requiredIf(
                    function () {
                        return (request()->get('is_receiver_country_specific') == true);
                    }
                ),
                Rule::in(Country::select('country_code')->get()->pluck('country_code'))
            ],
            'is_integration_specific' => 'boolean',
            'integration_code' => [
                'numeric',
                Rule::requiredIf(
                    function () {
                        return (request()->get('is_integration_specific') == true);
                    }
                ),
                Rule::in(Integration::select('integration_code')->get()->pluck('integration_code'))
            ],
            'send_option' => [
                'required',
                'string',
                Rule::in(PersonalizedMessage::SEND_OPTION)
            ],
            'send_time' => [
                Rule::requiredIf(
                    function () {
                        return (request()->get('send_option') == PersonalizedMessage::SEND_OPTION['SPECIFIC_TIME']);
                    }
                ),
            ],
            'bcc' => 'nullable|email',
            'subject' => 'nullable|string',
            'message_content' => 'required|string'
        ];

        return $this;
    }

    public function update(): Validation
    {
        $this->rules = [
            'message_hook_code' => [
                'numeric',
                'required',
                Rule::in(MessageHook::select('message_hook_code')->get()->pluck('message_hook_code'))
            ],
            'type' => [
                'required',
                'string',
                Rule::in(PersonalizedMessage::TEMPLATE_TYPE)
            ],
            'is_receiver_country_specific' => 'boolean',
            'receiver_country_code' => [
                'numeric',
                Rule::requiredIf(
                    function () {
                        return (request()->get('is_receiver_country_specific') == true);
                    }
                ),
                Rule::in(Country::select('country_code')->get()->pluck('country_code'))
            ],
            'is_integration_specific' => 'boolean',
            'integration_code' => [
                'numeric',
                Rule::requiredIf(
                    function () {
                        return (request()->get('is_integration_specific') == true);
                    }
                ),
                Rule::in(Integration::select('integration_code')->get()->pluck('integration_code'))
            ],
            'send_option' => [
                'required',
                'string',
                Rule::in(PersonalizedMessage::SEND_OPTION)
            ],
            'send_time' => [
                Rule::requiredIf(
                    function () {
                        return (request()->get('send_option') == PersonalizedMessage::SEND_OPTION['SPECIFIC_TIME']);
                    }
                ),
            ],
            'bcc' => 'nullable|email',
            'subject' => 'nullable|string',
            'message_content' => 'required|string'
        ];

        return $this;
    }
}
