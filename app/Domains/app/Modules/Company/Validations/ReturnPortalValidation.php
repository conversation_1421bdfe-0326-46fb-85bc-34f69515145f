<?php

namespace Main\Modules\Company\Validations;

use Illuminate\Validation\Rule;

use Core\Validation\AbstractValidation;
use Core\Contracts\Validation\Validation;
use Illuminate\Support\Facades\Route;
use Main\Modules\Location\Models\Country;
use Main\Modules\Location\Models\Language;
use Main\Modules\Platforms\Models\Integration;

class ReturnPortalValidation extends AbstractValidation
{
    public function store(): Validation
    {
        $this->rules = [
            'portal_name'  =>  [
                'required',
                'string',
                Rule::unique('return_portals')->where(function ($query) {
                    return $query->whereNull('deleted_at');
                }),
            ],
            'sender_country_code' => [
                'required',
                'numeric',
                Rule::in(Country::select('country_code')->get()->pluck('country_code'))
            ],
            'language_code' => [
                'required',
                'numeric',
                Rule::in(Language::select('language_code')->get()->pluck('language_code'))
            ],
            'integration_code' => [
                'required',
                'numeric',
                Rule::in(Integration::select('integration_code')->get()->pluck('integration_code'))
            ],
            'confirmation_note' => 'string | nullable',
            'is_active' => 'required | boolean'
        ];

        return $this;
    }

    public function update(): Validation
    {
        $returnPortalCode = Route::current()->parameters()['returnPortalCode'];
        $this->rules = [
            'portal_name'  =>  [
                'required',
                'string',
                Rule::unique('return_portals')
                    ->ignore($returnPortalCode, 'return_portal_code')
                    ->queryCallbacks(function ($query) {
                        return $query->select('portal_name')
                            ->whereNull('deleted_at')
                            ->orWhereNotNull('deleted_at')
                            ->get();
                    })
            ],
            'sender_country_code' => [
                'required',
                'numeric',
                Rule::in(Country::select('country_code')->get()->pluck('country_code'))
            ],
            'language_code' => [
                'required',
                'numeric',
                Rule::in(Language::select('language_code')->get()->pluck('language_code'))
            ],
            'integration_code' => [
                'required',
                'numeric',
                Rule::in(Integration::select('integration_code')->get()->pluck('integration_code'))
            ],
            'confirmation_note' => 'string | nullable',
            'is_active' => 'required | boolean'
        ];

        return $this;
    }

    public function updateReceiver(): Validation
    {
        $this->rules = [
            'company_name'  =>  'nullable | string',
            'attn'    =>  'nullable | string',
            'address' => 'required | string',
            'zipcode'   =>  'required | string',
            'country_code'   =>  [
                'required',
                'integer',
                Rule::in(
                    \Main\Modules\Location\Models\Country::select('country_code')
                        ->get()
                        ->pluck('country_code')
                )
            ],
            'city'    =>  'required | string',
            'email'    =>  'required | string',
            'phone'    =>  'required | string',
            'company_logo_url' => 'nullable | string',
            'logo_name' => 'nullable | string',
            'logo' => 'nullable | string'
        ];
        return $this;
    }

    public function updateText(): Validation
    {
        $this->rules = [
            'page_title'  =>  'required | string',
            'content'    =>  'nullable | string',
        ];
        return $this;
    }
}
