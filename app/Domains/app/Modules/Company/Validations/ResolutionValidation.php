<?php

namespace Main\Modules\Company\Validations;

use Illuminate\Validation\Rule;

use Core\Validation\AbstractValidation;
use Core\Contracts\Validation\Validation;
use Main\Modules\Company\Models\ReturnPortal;

class ResolutionValidation extends AbstractValidation
{
    public function store(): Validation
    {
        $this->rules = [
            'return_portal_code' => [
                'required',
                'numeric',
                Rule::in(ReturnPortal::select('return_portal_code')->get()->pluck('return_portal_code'))
            ],
            'available_days' => 'numeric | nullable',
            'description' => 'string | nullable',
            'name' => 'string | required'
        ];

        return $this;
    }

    public function update(): Validation
    {
        $this->rules = [
            'available_days' => 'numeric | nullable',
            'description' => 'string | nullable',
            'name' => 'string | nullable',
            'status' => 'boolean | nullable'
        ];

        return $this;
    }
}
