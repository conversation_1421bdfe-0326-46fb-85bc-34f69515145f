<?php

namespace Main\Modules\Company\Validations;

use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

use Core\Validation\AbstractValidation;
use Core\Contracts\Validation\Validation;

class CompanyValidation extends AbstractValidation
{
    public function store(): Validation
    {
        $this->rules = [
            'company_name'  =>  'required | string',
            'contact_person'    =>  'required | string',
            'zipcode'   =>  'required | string',
            'country_code'   =>  [
                'required',
                'integer',
                Rule::in(
                    \Main\Modules\Location\Models\Country::select('country_code')
                        ->get()
                        ->pluck('country_code')
                )
            ],
            'city'    =>  'required | string',
            'language_code'   =>  [
                'required',
                'integer',
                Rule::in(
                    \Main\Modules\Location\Models\Language::select('language_code')
                        ->get()
                        ->pluck('language_code')
                )
            ],
            'vat_no'    =>  'required | string',
            'phone_no'    =>  'required | string',
        ];

        return $this;
    }

    public function update(): Validation
    {
        $this->rules = [
            'company_name'  =>  'required | string',
            'contact_person'    =>  'required | string',
            'zipcode'   =>  'required | string',
            'country_code'   =>  [
                'integer',
                Rule::in(
                    \Main\Modules\Location\Models\Country::select('country_code')
                        ->get()
                        ->pluck('country_code')
                )
            ],
            'city'    =>  'required',
            'language_code'   =>  [
                'integer',
                Rule::in(
                    \Main\Modules\Location\Models\Language::select('language_code')
                        ->get()
                        ->pluck('language_code')
                )
            ],
            'vat_no'    =>  'string',
            'phone_no'    =>  'string',
            'address'    =>  'string',
            'attn'    =>  'string',
            'website'    =>  'url',
            'company_logo' => 'nullable | image| max:10240'
        ];

        return $this;
    }

    public function register(): Validation
    {
        $this->rules = [
            'company_name'  =>  'required | string',
            'contact_person'    =>  'required | string',
            'zipcode'   =>  'required | string',
            'address'   =>  'string',
            'country_code'   =>  [
                'required',
                'integer',
                Rule::in(
                    \Main\Modules\Location\Models\Country::select('country_code')
                        ->get()
                        ->pluck('country_code')
                )
            ],
            'city'    =>  'required | string',
            'language_code'   =>  [
                'required',
                'integer',
                Rule::in(
                    \Main\Modules\Location\Models\Language::select('language_code')
                        ->get()
                        ->pluck('language_code')
                )
            ],
            'vat_no'    =>  'required | string',
            'phone_no'    =>  'required | string',
            'email' =>  [
                'required',
                'email',
                'unique:Main\Modules\User\Models\User,email'
            ],
            'password'  =>  [
                'required',
                'confirmed',
                Password::min(8)
            ]
        ];

        return $this;
    }
}
