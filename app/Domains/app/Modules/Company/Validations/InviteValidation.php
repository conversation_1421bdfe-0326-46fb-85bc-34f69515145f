<?php

namespace Main\Modules\Company\Validations;

use Core\Validation\AbstractValidation;
use Core\Contracts\Validation\Validation;

class InviteValidation extends AbstractValidation
{
    public function store(): Validation
    {
        $this->rules = [
            'name'  =>  'required | string',
            'email'    =>  'required | string',
            'permitted_modules'   =>  'required | array',
            'permitted_modules.*'   =>  'integer'
        ];

        return $this;
    }

    public function update(): Validation
    {
        $this->rules = [
            'name'  =>  'required | string',
            'email'    =>  'required | string',
            'permitted_modules'   =>  'required | array',
            'permitted_modules.*'   =>  'integer'
        ];
        
        return $this;
    }
}