<?php

namespace Main\Modules\Company\Validations;

use Illuminate\Validation\Rule;

use Core\Validation\AbstractValidation;
use Core\Contracts\Validation\Validation;
use Main\Modules\Company\Models\ReturnPortal;
use Main\Modules\Setting\Models\Currency;
use Main\Modules\Shipment\Models\Template;

class MethodValidation extends AbstractValidation
{
    public function store(): Validation
    {
        $this->rules = [
            'return_portal_code' => [
                'required',
                'numeric',
                Rule::in(ReturnPortal::select('return_portal_code')->get()->pluck('return_portal_code'))
            ],
            'template_code' => [
                'nullable',
                'numeric',
                Rule::in(Template::select('template_code')->get()->pluck('template_code'))
            ],
            'description' => 'string | nullable',
            'name' => 'string | required',
            'parcelshop_code' => 'string | nullable',
            'price_type' => 'string | required',
            'currency_code' => [
                'required',
                'numeric',
                Rule::in(Currency::select('currency_code')->get()->pluck('currency_code'))
            ],
            'price' => 'required | numeric'
        ];

        return $this;
    }

    public function update(): Validation
    {
        $this->rules = [
            'return_portal_code' => [
                'required',
                'numeric',
                Rule::in(ReturnPortal::select('return_portal_code')->get()->pluck('return_portal_code'))
            ],
            'template_code' => [
                'nullable',
                'numeric',
                Rule::in(Template::select('template_code')->get()->pluck('template_code'))
            ],
            'description' => 'string | nullable',
            'name' => 'string | required',
            'parcelshop_code' => 'string | nullable',
            'price_type' => 'string | required',
            'currency_code' => [
                'required',
                'numeric',
                Rule::in(Currency::select('currency_code')->get()->pluck('currency_code'))
            ],
            'price' => 'required | numeric'
        ];

        return $this;
    }
}
