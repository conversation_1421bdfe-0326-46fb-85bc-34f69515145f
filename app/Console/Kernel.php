<?php

namespace App\Console;

use App\Jobs\CreateStandardTemplatesForCustomerJob;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

use App\Console\Commands\ImportOrdersCommand;
use App\Console\Commands\ImportOrderDetailsCommand;
use App\Console\Commands\ImportProductsCommand;
use App\Console\Commands\ImportCustomersCommand;
use App\Console\Commands\UpdateShipmentsCommand;
use App\Console\Commands\CreateMonthlyInvoiceCommand;
use App\Console\Commands\GenerateCreditInvoicesCommand;
use App\Jobs\SyncOrderAuthorizationStatuses;
use Main\Modules\User\Models\User;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('queue:work --queue=shipments --stop-when-empty')->hourlyAt(24)->sendOutputTo(storage_path().'/logs/shipments-harvester.log');
        // $schedule->command('queue:work --sleep=8 --tries=1 --queue=orders-webhook --daemon');

        $schedule->command(UpdateShipmentsCommand::class)->cron('0 0,6,12,18 * * *');

        // create monthly invoices command
        $schedule->command(CreateMonthlyInvoiceCommand::class)->monthly();

        // update shopify order statuses
        $schedule->job((new SyncOrderAuthorizationStatuses)->onQueue('orders'))->hourly();

        // generate invoices through economics
        $schedule->command(GenerateCreditInvoicesCommand::class)->daily();

        // creating templates for newly created user
        $schedule->call(function () {
            $user = User::where('is_new_created', 1)->first();
            if ($user) {
                dispatch(new CreateStandardTemplatesForCustomerJob($user))->onQueue('create-standard-template');
            }
        })->everyMinute();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
