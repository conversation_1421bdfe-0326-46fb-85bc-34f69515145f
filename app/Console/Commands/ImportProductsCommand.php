<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use Main\Jobs\ProductHarvester;

class ImportProductsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:products';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import products for webshop';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $integrations = \Main\Modules\Platforms\Models\Integration::with('platform')->get();

        foreach($integrations as $integration)
        {
            if($integration->platform !== null) {
                $integrationData = array_merge($integration->toArray(), ['platform_name'=>$integration->platform->name]);

                if($integration->api_url !== '') {
                    ProductHarvester::dispatch($integrationData)->onQueue('products');
                }
            }
        }

        return 1;
    }
}
