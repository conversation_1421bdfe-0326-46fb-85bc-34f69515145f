<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Main\Jobs\CreateMonthlyInvoiceJob;

// use Main\Jobs\ShipmentHarvester;

class CreateMonthlyInvoiceCommand extends Command
{
    protected $signature = 'monthly:invoice {--debug : Run in debug mode}';
    protected $description = 'Create monthly invoices for all eligible companies';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting monthly invoice generation...');
        try {
            if ($this->option('debug')) {
                $this->warn('Running in debug mode - will process immediately');
                (new CreateMonthlyInvoiceJob())->handle();
            } else {
                CreateMonthlyInvoiceJob::dispatch()->onQueue('monthly-invoice');
                $this->info('Monthly invoice job has been queued successfully');
            }

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("Failed to queue monthly invoice job: {$e->getMessage()}");
            return Command::FAILURE;
        }
    }
}
