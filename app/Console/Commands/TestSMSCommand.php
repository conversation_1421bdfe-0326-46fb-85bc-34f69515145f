<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Main\Events\NotifyCustomerEvent;
use Main\Modules\Order\Models\Customer;

class TestSMSCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:sms';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Twilio integration';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $recipients = ['+9779841296624'];
            $customer = Customer::find(2696214503);
            event(new NotifyCustomerEvent(3965563676, 1027, $customer, 'invoice_created'));
            // $client = new Client(config('sms.twilio.id'), config('sms.twilio.token'));

            // foreach($recipients as $recipient) {
            //     var_dump($recipient);
            //     $client->messages->create((string)$recipient, [
            //         'from' => config('sms.twilio.from'),
            //         'body' => 'Hi team! This is test message sent from the app for testing twilio integration'
            //     ]);
            // }
        } catch(\Exception $e) {
            var_dump($e->getMessage());
        }
        return 1;
    }
}
