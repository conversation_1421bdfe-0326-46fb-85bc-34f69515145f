<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        //   $email = $this->argument('email');
        $email = '<EMAIL>';
        $this->info("sending email to: {$email}");
        $data = [
            'email' => $email,
            'name' => 'Test User!',
            'pdf_url' => 'https://laravel.com/docs/10.x/artisan',
            'footer' => 'Shipvagoo ApS | Vestergade 18E, 1., 1456 København K | Danmark,Shipvagoo ApS | Vestergade 18E, 1., 1456 København K | Danmark'
        ];
        Mail::send('email.test.add-funds', $data, function ($message) use ($data) {
            $message->to($data['email']);
            $message->subject('It is testing email Subject, Ignore it');
        });
        $this->info("Email has been sent successfully.");
    }
}
