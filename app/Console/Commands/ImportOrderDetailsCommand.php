<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Main\Jobs\OrderDetailHarvester;
use Main\Modules\Order\Models\Order;

class ImportOrderDetailsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:order-details';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import order details for webshop';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $orders = Order::with(['company', 'integration', 'integration.platform'])->where('is_touched', '=', 0)->get();
        foreach ($orders as $order) {
            if ($order->integration) {
                $integration = $order->integration->toArray();
                $platform = isset($order->integration->platform) ? $order->integration->platform->name : 'Shopifyyy';
                $integrationData = array_merge($integration, ['platform_name' => $platform]);
                OrderDetailHarvester::dispatch($integrationData, (int)$order->order_id)->onQueue('order-details');
            }
        }

        return 1;
    }
}
