<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Main\Jobs\ShipmentHarvester;

class UpdateShipmentsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:shipments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update shipments status';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        info("job for updateshipment command is running");
        ShipmentHarvester::dispatch()->onQueue('shipments');
        // $integrations = \Main\Modules\Platforms\Models\Integration::with('platform')->get();

        // foreach($integrations as $integration)
        // {
        //     if($integration->platform !== null) {
        //         $integrationData = array_merge($integration->toArray(), ['platform_name'=>$integration->platform->name]);

        //         if($integration->api_url !== '') {
        //             CustomerHarvester::dispatch($integrationData)->onQueue('customers');
        //         }
        //     }
        // }

        return 1;
    }
}
