<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Main\Modules\Transaction\Services\CreditInvoiceService;
use App\Domains\app\Services\CreditInvoice\CreditInvoiceEconomicService;
use Main\Modules\EconomicInvoice\Services\EconomicInvoiceService;

class GenerateCreditInvoicesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'credit:generate-invoices';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate credit invoices for companies at the end of their billing cycle';

    private EconomicInvoiceService $economicInvoiceService;
    private CreditInvoiceEconomicService $economicService;

    public function __construct()
    {
        parent::__construct();
        $this->economicInvoiceService = app(EconomicInvoiceService::class);
        $this->economicService = app(CreditInvoiceEconomicService::class);

    }

    public function handle()
    {
        try {
            $this->info('Starting credit invoice generation...');
            $invoices = $this->economicInvoiceService->generateCreditInvoices();

            $this->info('Generated ' . count($invoices) . ' credit invoices');

            // create draft invoices in e-conomics system here
            $this->economicService->postDraftInvoice($invoices, 1234, 1234);

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("Failed to generate credit invoices: {$e->getMessage()}");
            return Command::FAILURE;
        }
    }
}
